// 添加类型定义
interface ApiValidationResponse {
  success: boolean
  error?: string
}

// Cookie 相关类型定义
interface CookieData {
  name: string
  value: string
  domain: string
}

// 处理 cookie 存储
async function storeCookies(cookies: CookieData[]) {
  try {
    for (const cookie of cookies) {
      await chrome.cookies.set({
        name: cookie.name,
        value: cookie.value,
        url: `https://${cookie.domain}`,
        domain: cookie.domain,
        path: '/',
        secure: true,
        httpOnly: true
      })
    }
    return true
  } catch (error) {
    // Error storing cookies
    return false
  }
}

// 添加 API key 验证处理
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'validateApiKey') {
    // 立即返回，表示我们会异步处理
    sendResponse({ pending: true })

    // 使用 fetch 的 AbortController 来处理超时
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 5000) // 5秒超时

    validateApiKey(message.provider, message.key, controller.signal)
      .then(result => {
        // 直接使用 runtime.sendMessage 发送结果
        chrome.runtime.sendMessage({
          type: 'apiKeyValidationResult',
          provider: message.provider,
          result
        })
      })
      .finally(() => clearTimeout(timeoutId))

    return false
  }
})

// API key 验证函数
async function validateApiKey(
  provider: string,
  key: string,
  signal: AbortSignal
): Promise<ApiValidationResponse> {
  try {
    let endpoint: string
    let headers: Record<string, string> = {
      'Content-Type': 'application/json'
    }

    switch (provider) {
      case 'openai':
        endpoint = 'https://api.openai.com/v1/models'
        headers.Authorization = `Bearer ${key}`
        break
      case 'claude':
        endpoint = 'https://api.anthropic.com/v1/models'
        headers['x-api-key'] = key
        headers['anthropic-version'] = '2023-06-01'
        break
      case 'moonshot':
        endpoint = 'https://api.moonshot.cn/v1/models'
        headers.Authorization = `Bearer ${key}`
        break
      case 'gemini':
        endpoint = `https://generativelanguage.googleapis.com/v1/models?key=${key}`
        break
      case 'deepseek':
        endpoint = 'https://api.deepseek.com/v1/models'
        headers.Authorization = `Bearer ${key}`
        break
      default:
        throw new Error('不支持的 AI 提供商')
    }

    const response = await fetch(endpoint, {
      method: 'GET',
      headers,
      signal // 添加 signal 以支持超时中断
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error?.message || `Invalid API Key (${response.status})`)
    }

    return {
      success: true
    }
  } catch (error) {
    if (error instanceof DOMException && error.name === 'AbortError') {
      return {
        success: false,
        error: 'Request timeout, please check your network connection'
      }
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Validation failed'
    }
  }
}