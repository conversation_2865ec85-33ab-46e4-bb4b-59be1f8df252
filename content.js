// Constants
const STORAGE_KEYS = {
  LAST_MODE: 'formify_last_mode',
  PROJECTS: 'formify_projects',
  SKIP_LOGIN: 'formify_skip_login',
  LAST_LANGUAGE: 'formify_last_language'
};

// 屏蔽特定错误消息
const errorMessages = [
  'Extension context invalidated',
  'Could not establish connection. Receiving end does not exist',
  'The message port closed before a response was received'
];

// 重写 console.error 来屏蔽特定错误
const originalConsoleError = console.error;
console.error = function(...args) {
  const shouldBlock = errorMessages.some(msg => 
    args.some(arg => 
      arg instanceof Error ? arg.message.includes(msg) : String(arg).includes(msg)
    )
  );

  if (!shouldBlock) {
    originalConsoleError.apply(console, args);
  }
};

// 处理未捕获的 Promise 错误
window.addEventListener('unhandledrejection', function(event) {
  if (errorMessages.some(msg => event.reason?.message?.includes(msg))) {
    event.preventDefault();
    event.stopPropagation();
  }
});

// 处理常规错误
window.addEventListener('error', function(event) {
  if (errorMessages.some(msg => event.error?.message?.includes(msg))) {
    event.preventDefault();
    event.stopPropagation();
  }
}, true);

// Logger utility with debug control
const DEBUG = {
  enabled: false,    // 关闭总开关
  form: false,       // 关闭表单检测相关日志
  fill: false,       // 关闭表单填充相关日志
  api: false         // 关闭API响应相关日志
};

const Logger = {
  _log: (type, category, message, data = null) => {
    if (!DEBUG.enabled || !DEBUG[category]) return;
    
    const icons = {
      info: 'ℹ️',
      success: '✅',
      error: '❌',
      debug: '🔍'
    };
    
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    console.log(`[Formify ${timestamp}] ${icons[type]} [${category}] ${message}`, data || '');
  },

  info: (category, message, data = null) => {
    Logger._log('info', category, message, data);
  },
  
  success: (category, message, data = null) => {
    Logger._log('success', category, message, data);
  },
  
  error: (message, error) => {
    // 错误日志始终输出
    console.error(`[Formify] ❌ ${message}`, error);
  },
  
  debug: (category, message, data = null) => {
    Logger._log('debug', category, message, data);
  }
};

// Form field detection
function detectFormFields() {
  const formFields = [];
  
  Logger.debug('form', 'Starting form field detection');
  
  // 获取所有可能的表单元素
  const allTextboxes = document.querySelectorAll('[role="textbox"]');
  const allInputs = document.querySelectorAll('input');
  const allContentEditables = document.querySelectorAll('[contenteditable="true"]');
  
  Logger.debug('form', 'Found base elements count', {
    textboxes: allTextboxes.length,
    inputs: allInputs.length,
    contentEditables: allContentEditables.length
  });

  // Outlook 字段
  const outlookRecipientInput = document.querySelector('div[role="textbox"].EditorClass');
  const outlookSubjectInput = document.querySelector('input.fui-Input__input.zrkM_');
  const outlookBodyInput = document.querySelector('div[role="textbox"].DziEn');
  
  // Gmail 字段
  const gmailRecipientInput = document.querySelector('input.agP.aFw[role="combobox"]');
  const gmailSubjectInput = document.querySelector('input.aoT[name="subjectbox"]');
  const gmailBodyInput = document.querySelector('div.Am.aiL.Al.editable.LW-avf[contenteditable="true"]');
  
  // 添加 Outlook 字段（如果存在）
  if (outlookRecipientInput || outlookSubjectInput || outlookBodyInput) {
    if (outlookRecipientInput) {
      formFields.push({
        element: outlookRecipientInput,
        type: 'contenteditable',
        id: outlookRecipientInput.id,
        name: 'to',
        placeholder: '',
        label: 'To'
      });
    }
    if (outlookSubjectInput) {
      formFields.push({
        element: outlookSubjectInput,
        type: 'text',
        id: outlookSubjectInput.id,
        name: 'subject',
        placeholder: outlookSubjectInput.placeholder,
        label: 'Subject'
      });
    }
    if (outlookBodyInput) {
      formFields.push({
        element: outlookBodyInput,
        type: 'contenteditable',
        id: outlookBodyInput.id,
        name: 'body',
        placeholder: '',
        label: 'Body'
      });
    }
  }

  // 添加 Gmail 字段（如果存在）
  if (gmailRecipientInput || gmailSubjectInput || gmailBodyInput) {
    if (gmailRecipientInput) {
      formFields.push({
        element: gmailRecipientInput,
        type: 'text',
        id: gmailRecipientInput.id,
        name: 'to',
        placeholder: '',
        label: 'To'
      });
    }
    if (gmailSubjectInput) {
      formFields.push({
        element: gmailSubjectInput,
        type: 'text',
        id: gmailSubjectInput.id,
        name: 'subject',
        placeholder: gmailSubjectInput.placeholder,
        label: 'Subject'
      });
    }
    if (gmailBodyInput) {
      formFields.push({
        element: gmailBodyInput,
        type: 'contenteditable',
        id: gmailBodyInput.id,
        name: 'body',
        placeholder: '',
        label: 'Body'
      });
    }
  }

  // GitHub 特定字段查询
  const githubTitleInput = document.querySelector('input[aria-label="Add a title"], input[aria-label="Title"]');
  const githubDescInput = document.querySelector(`
    textarea.MarkdownInput-module__textArea--QjIwG,
    textarea[name="description"],
    textarea[name="comment[body]"],
    .comment-form-textarea
  `);
  const githubTaskList = document.querySelector('.task-list-item, .contains-task-list');

  // 如果找到 GitHub 特定字段，优先使用它们
  if (githubTitleInput || githubDescInput || githubTaskList) {
    if (githubTitleInput) {
      formFields.push({
        element: githubTitleInput,
        type: 'text',
        id: githubTitleInput.id,
        name: 'title',
        placeholder: githubTitleInput.placeholder,
        label: 'Title'
      });
    }
    if (githubDescInput) {
      formFields.push({
        element: githubDescInput,
        type: 'textarea',
        id: githubDescInput.id,
        name: 'description',
        placeholder: githubDescInput.placeholder,
        label: 'Description'
      });
    }
    if (githubTaskList) {
      formFields.push({
        element: githubTaskList,
        type: 'checkbox',
        id: githubTaskList.id,
        name: 'task-list',
        label: 'Task List'
      });
    }
  }

  // 标准表单字段查询
  const standardInputs = document.querySelectorAll('input:not([type="hidden"]), textarea, select, .form-control');

  standardInputs.forEach(input => {
    // 避免重复添加已检测到的特定字段
    const isAlreadyAdded = formFields.some(field => field.element === input);
    if (!isAlreadyAdded) {
      const label = findLabel(input);
      const field = {
        element: input,
        type: input.type || input.tagName.toLowerCase(),
        id: input.id,
        name: input.name,
        placeholder: input.placeholder,
        label: label
      };
      formFields.push(field);
    }
  });

  return formFields;
}

function findLabel(input) {
  // Try to find label by for attribute
  if (input.id) {
    const label = document.querySelector(`label[for="${input.id}"]`);
    if (label) return label.textContent.trim();
  }

  // Try to find parent label
  const parentLabel = input.closest('label');
  if (parentLabel) return parentLabel.textContent.trim();

  // Try to find nearby text that might be a label
  const previousElement = input.previousElementSibling;
  if (previousElement && previousElement.tagName !== 'INPUT' && previousElement.tagName !== 'TEXTAREA') {
    return previousElement.textContent.trim();
  }

  return '';
}

async function generateFormContent(data) {
  Logger.debug('api', 'Starting to generate form content', data);
  const formFields = detectFormFields();
  const fieldDescriptions = formFields.map(field => {
    return {
      type: field.type,
      label: field.label,
      placeholder: field.placeholder,
      name: field.name
    };
  });

  // 获取所有需要的设置
  const storage = await chrome.storage.sync.get([
    'formify_settings', 
    'formify_projects', 
    'formify_api_keys'
  ]);
  
  const settings = storage.formify_settings || {};
  const apiKeys = storage.formify_api_keys || {};
  const useCustomApi = settings.useCustomApi || false;

  let projectInfo = null;
  if (data.mode === 'bugReport' && data.projectId) {
    const projects = storage.formify_projects || [];
    projectInfo = projects.find(p => p.id === data.projectId);
  }

  let provider = 'deepseek';
  let model = undefined;
  let apiKey = undefined;
  
  if (useCustomApi) {
    provider = settings.defaultProvider || 'openai';
    model = settings.defaultModel;
    apiKey = apiKeys[provider];
    
    if (!apiKey) {
      throw new Error(`No API key found for ${provider}. Please set it in the extension settings.`);
    }
  }

  // Get user selected language
  const languageStorage = await chrome.storage.local.get(STORAGE_KEYS.LAST_LANGUAGE);
  const language = languageStorage[STORAGE_KEYS.LAST_LANGUAGE] || 'en'; // 默认英语

  try {
    const requestBody = {
      mode: data.mode,
      description: data.description,
      projectId: data.projectId,
      project: projectInfo,  // 添加完整的项目信息
      formFields: fieldDescriptions,
      useCustomApi,
      userId: settings.userId,
      language, // 添加语言参数
      provider,  // 添加 provider
      model     // 添加 model
    };

    if (useCustomApi) {
      requestBody.apiKey = apiKey;
    }

    const response = await fetch('https://fillify-343190162770.asia-east1.run.app/api/v1/form/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'chrome-extension://' + chrome.runtime.id
      },
      credentials: 'include',
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to generate form content');
    }

    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.message || 'Failed to generate form content');
    }

    let formContent;
    try {
      formContent = typeof result.data.content === 'string' 
        ? JSON.parse(result.data.content)
        : result.data.content;
    } catch (error) {
      throw new Error('Invalid JSON response from server');
    }
    
    // 只在使用自定义 API 时更新 token 统计
    if (useCustomApi && result.data.usage) {
      // Update token usage statistics silently
      const stats = await chrome.storage.sync.get('formify_token_stats');
      const tokenStats = stats.formify_token_stats || {};

      if (!tokenStats[provider]) {
        tokenStats[provider] = {
          promptTokens: 0,
          completionTokens: 0,
          totalTokens: 0,
          lastUpdated: new Date().toISOString()
        };
      }

      tokenStats[provider].promptTokens += result.data.usage.prompt_tokens;
      tokenStats[provider].completionTokens += result.data.usage.completion_tokens;
      tokenStats[provider].totalTokens += result.data.usage.total_tokens;
      tokenStats[provider].lastUpdated = new Date().toISOString();

      await chrome.storage.sync.set({ formify_token_stats: tokenStats });
    }
    
    Logger.debug('api', 'API response content', formContent);
    return formContent;
  } catch (error) {
    Logger.error('Failed to generate form content', error);
    throw error;
  }
}

async function fillForm(data, callback) {
  Logger.debug('fill', 'Starting to fill form', data);
  const formFields = detectFormFields();
  
  const styleTag = document.createElement('style');
  styleTag.textContent = `
    @keyframes formifyBorderGlow {
      0% {
        outline: 2px solid rgba(33, 150, 243, 0.4);
        box-shadow: 0 0 5px rgba(33, 150, 243, 0.4);
      }
      50% {
        outline: 2px solid rgba(33, 150, 243, 0.8);
        box-shadow: 0 0 15px rgba(33, 150, 243, 0.6);
      }
      100% {
        outline: 2px solid rgba(33, 150, 243, 0.4);
        box-shadow: 0 0 5px rgba(33, 150, 243, 0.4);
      }
    }
    .formify-loading {
      animation: formifyBorderGlow 1.5s ease-in-out infinite !important;
      z-index: 9999;
      position: relative;
    }
  `;
  document.head.appendChild(styleTag);

  formFields.forEach(field => {
    if (field.element) {
      field.element.classList.add('formify-loading');
    }
  });

  try {
    Logger.info('Generating form content...');
    const formContent = await generateFormContent(data);
    Logger.success('Form content generated successfully:', formContent);
    
    // 先处理主题字段
    const subjectField = formFields.find(field => field.name === 'subject');
    if (subjectField) {
      Logger.info('Processing subject field...');
      try {
        // 检查是否是 React 组件
        const isReactComponent = subjectField.element.classList.contains('fui-Input__input');

        if (isReactComponent) {
          // 使用 React 组件的处理方式
          const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value").set;
          nativeInputValueSetter.call(subjectField.element, formContent.subject);
          
          // 触发一系列事件来确保 React 状态更新
          ['input', 'change', 'blur'].forEach(eventType => {
            const event = new Event(eventType, {
              bubbles: true,
              composed: true,
              cancelable: true
            });
            subjectField.element.dispatchEvent(event);
          });
          
          // 确保元素获得焦点
          subjectField.element.focus();
          
          // 创建并触发输入事件
          const inputEvent = new InputEvent('input', {
            bubbles: true,
            cancelable: true,
            inputType: 'insertText',
            data: formContent.subject
          });
          subjectField.element.dispatchEvent(inputEvent);
          
          // 最后失去焦点
          subjectField.element.blur();
        } else {
          // 如果不是 React 组件，使用普通方式
          subjectField.element.value = formContent.subject;
          const event = new Event('change', { bubbles: true });
          subjectField.element.dispatchEvent(event);
        }
      } catch (e) {}
    }

    // 处理其他字段
    formFields.forEach(field => {
      // 跳过主题字段，因为已经处理过了
      if (field.name === 'subject') return;

      let content;
      if (field.name === 'to') {
        content = formContent.to || formContent.recipient;
        Logger.debug('fill', `Matching recipient field: ${field.name}`, { content });
      } else if (field.name === 'body') {
        content = formContent[''] || formContent.body || formContent.content;
        Logger.debug('fill', `Matching body field: ${field.name}`, { content });
      } else if (field.name === 'title' || field.name === 'description') {
        content = formContent[field.name];
      } else {
        // 更通用的内容匹配逻辑
        const labelNoAsterisk = field.label?.replace(/\s*\*\s*$/, '') || '';
        const labelFirstWord = field.label?.split(/[\s*]/)[0] || '';
        

        // 创建一个根据字段语言特性进行匹配的函数
        const findValueByLanguage = (obj, key) => {
          if (!key) return undefined;
          
          // 检查是否包含非英文字符
          const hasNonEnglish = /[^\x00-\x7F]/.test(key);
          
          if (hasNonEnglish) {
            // 对于非英文字段，使用更灵活的匹配
            const foundKey = Object.keys(obj).find(k => {
              // 移除两边的空格和星号后比较
              const normalizedK = k.replace(/\s*\*\s*$/, '').trim();
              const normalizedKey = key.replace(/\s*\*\s*$/, '').trim();
              return normalizedK === normalizedKey;
            });
            return foundKey ? obj[foundKey] : undefined;
          } else {
            // 对于英文字段，保持不区分大小写的匹配
            const lowerKey = key.toLowerCase();
            const foundKey = Object.keys(obj).find(k => k.toLowerCase() === lowerKey);
            return foundKey ? obj[foundKey] : undefined;
          }
        };

        content = findValueByLanguage(formContent, field.name) ||           // 优先使用 name 属性
                 findValueByLanguage(formContent, field.id) ||              // 其次使用 id
                 findValueByLanguage(formContent, labelNoAsterisk) ||       // 尝试使用不带星号的 label
                 findValueByLanguage(formContent, labelFirstWord);          // 尝试使用 label 的第一个单词
        
        Logger.debug('fill', `Field matching results`, {
          fieldName: field.name,
          fieldId: field.id,
          label: field.label,
          labelNoAsterisk,
          labelFirstWord,
          matchedContent: content
        });
      }
      

      if (content && field.element.type !== 'file') {
        try {
          // 检查是否是 Outlook 的 contenteditable 元素
          const isOutlookContentEditable = field.type === 'contenteditable' && 
                                         field.element.hasAttribute('contenteditable') &&
                                         field.element.getAttribute('role') === 'textbox';
          

          if (isOutlookContentEditable) {
            try {
              // 聚焦元素
              field.element.focus();
              
              // 清空现有内容
              field.element.innerHTML = '';
              
              // 创建并插入文本节点
              const textNode = document.createTextNode(content);
              field.element.appendChild(textNode);
              
              // 触发必要的事件
              ['input', 'change', 'blur'].forEach(eventType => {
                const event = new Event(eventType, { bubbles: true, composed: true });
                field.element.dispatchEvent(event);
              });
              
              // 如果是收件人字段，额外处理
              if (field.name === 'to') {
                // 模拟按下回车键
                const enterEvent = new KeyboardEvent('keydown', {
                  key: 'Enter',
                  code: 'Enter',
                  keyCode: 13,
                  which: 13,
                  bubbles: true,
                  cancelable: true
                });
                field.element.dispatchEvent(enterEvent);
              }
            } catch (e) {
              Logger.error('Failed to set Outlook contenteditable element value:', e);
            }
          } else {
            // 检查是否是 GitHub 的特定组件
            const isGitHubComponent = field.element.classList.contains('MarkdownInput-module__textArea--QjIwG') ||
                                    field.element.getAttribute('aria-label') === 'Add a title';

            // 检查是否是选择器元素
            const isSelectElement = field.element.tagName.toLowerCase() === 'select';

            // 检测是否可能是 React 受控组件
            const isReactComponent = !isGitHubComponent && !isSelectElement && (
              field.element.hasAttribute('data-reactroot') ||
              /sc-|styled-|react-/.test(field.element.className) ||
              Array.from(field.element.attributes).some(attr => 
                attr.name.startsWith('data-') && 
                !['data-id', 'data-name', 'data-type'].includes(attr.name)
              ) ||
              field.element.hasAttribute('className')
            );

            if (isSelectElement) {
              try {
                // 找到匹配的选项
                const options = Array.from(field.element.options);
                const matchingOption = options.find(option => 
                  option.text.toLowerCase().includes(content.toLowerCase()) || 
                  option.value.toLowerCase() === content.toLowerCase()
                );

                if (matchingOption) {
                  // 设置选中值
                  field.element.value = matchingOption.value;
                  
                  // 触发必要的事件
                  ['input', 'change', 'focus', 'blur'].forEach(eventType => {
                    const event = new Event(eventType, { bubbles: true });
                    field.element.dispatchEvent(event);
                  });
                }
              } catch (e) {
                Logger.error('Failed to set selector value:', e);
              }
            } else if (isGitHubComponent) {
              // GitHub 特定处理
              if (field.element.tagName.toLowerCase() === 'textarea') {
                try {
                  const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLTextAreaElement.prototype, 'value').set;
                  nativeInputValueSetter.call(field.element, content);
                } catch (e) {
                  field.element.value = content;
                }
              } else {
                field.element.value = content;
              }
              
              // 触发必要的事件来确保 GitHub 的表单状态更新
              ['input', 'change', 'blur'].forEach(eventType => {
                const event = new Event(eventType, { bubbles: true });
                field.element.dispatchEvent(event);
              });
            } else if (isReactComponent) {
              try {
                const elementType = field.element.tagName.toLowerCase();
                const elementClass = `HTML${elementType.charAt(0).toUpperCase() + elementType.slice(1)}Element`;
                const elementPrototype = window[elementClass]?.prototype;
                
                if (elementPrototype) {
                  const propertyDescriptor = Object.getOwnPropertyDescriptor(elementPrototype, 'value');
                  if (propertyDescriptor && propertyDescriptor.set) {
                    propertyDescriptor.set.call(field.element, content);
                  } else {
                    field.element.value = content;
                  }
                } else {
                  field.element.value = content;
                }
              } catch (e) {
                Logger.error('Failed to set React component value, trying direct assignment:', e);
                field.element.value = content;
              }
              
              // 触发事件
              ['input', 'change'].forEach(eventType => {
                const event = new Event(eventType, { bubbles: true });
                field.element.dispatchEvent(event);
              });
            } else {
              // 对于非 React 组件，使用标准的 DOM 更新
              field.element.value = content;
              const event = new Event('change', { bubbles: true });
              field.element.dispatchEvent(event);
            }
          }
        } catch (e) {
          // 如果特殊处理失败，尝试最基本的更新
          try {
            field.element.value = content;
          } catch (err) {
            Logger.error('Basic value assignment failed:', err);
          }
        }
      }
    });

    // 移除加载动画
    formFields.forEach(field => {
      if (field.element) {
        field.element.classList.remove('formify-loading');
      }
    });
    
    if (callback) {
      callback(true);
    }
  } catch (error) {
    // 移除加载动画
    formFields.forEach(field => {
      if (field.element) {
        field.element.classList.remove('formify-loading');
      }
    });
    
    if (callback) {
      callback(false, error.message || 'Failed to fill form');
    }
  } finally {
    // 清理样式
    if (styleTag && styleTag.parentNode) {
      styleTag.parentNode.removeChild(styleTag);
    }
  }
}

// 统一的消息处理
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  try {
    switch (message.action || message.type) {
      case 'fillForm':
        fillForm(message.data, (success, error) => {
          if (success) {
            sendResponse(true);
          } else {
            sendResponse({ error });
          }
        });
        return true;
      
      case 'checkFormFields':
        const formFields = detectFormFields();
        sendResponse({
          hasFields: formFields.length > 0,
          fieldCount: formFields.length
        });
        break;
    }
  } catch (error) {
    sendResponse({ error: error.message || 'An unexpected error occurred' });
  }
});
