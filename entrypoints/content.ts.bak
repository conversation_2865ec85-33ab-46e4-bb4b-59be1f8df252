export default defineContentScript({
  matches: ['<all_urls>'],  // 匹配所有 URL
  main() {
    // 等待 DOM 加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', setupMessageListener);
    } else {
      setupMessageListener();
    }
    
    Logger.debug('form', 'Content script initialized')
  }
})

// 类型定义
interface FormField {
  element: HTMLElement
  type: string
  id: string
  name: string
  placeholder?: string
  label?: string
}

interface Logger {
  _log: (type: 'info' | 'success' | 'error' | 'debug', category: string, message: string, data?: any) => void
  info: (category: string, message: string, data?: any) => void
  success: (category: string, message: string, data?: any) => void
  error: (message: string, error: any) => void
  debug: (category: string, message: string, data?: any) => void
}

interface Debug {
  enabled: boolean
  form: boolean
  fill: boolean
  api: boolean
  message: boolean
  [key: string]: boolean  // 添加索引签名
}

interface StorageKeys {
  LAST_MODE: string
  PROJECTS: string
  SKIP_LOGIN: string
  LAST_LANGUAGE: string
}

interface FormContent {
  to?: string
  recipient?: string
  subject?: string
  body?: string
  content?: string
  title?: string
  description?: string
  [key: string]: string | undefined
}

interface FillFormCallback {
  success?: () => void;
  error?: (error: any) => void;
}

// Constants
const STORAGE_KEYS: StorageKeys = {
  LAST_MODE: 'formify_last_mode',
  PROJECTS: 'formify_projects',
  SKIP_LOGIN: 'formify_skip_login',
  LAST_LANGUAGE: 'formify_last_language'
}

// 屏蔽特定错误消息
const errorMessages: string[] = [
  'Extension context invalidated',
  'Could not establish connection. Receiving end does not exist',
  'The message port closed before a response was received'
]

// Debug configuration
const DEBUG: Debug = {
  enabled: true,    // 开启总开关
  form: true,       // 开启表单检测相关日志
  fill: true,       // 开启表单填充相关日志
  api: true,        // 开启API响应相关日志
  message: true     // 开启消息相关日志
}

// 重写 console.error 来屏蔽特定错误
const originalConsoleError = console.error
console.error = function(...args: any[]) {
  const shouldBlock = errorMessages.some(msg => 
    args.some(arg => 
      arg instanceof Error ? arg.message.includes(msg) : String(arg).includes(msg)
    )
  )

  if (!shouldBlock) {
    originalConsoleError.apply(console, args)
  }
}

// Logger utility
const Logger: Logger = {
  _log: (type, category, message, data = null) => {
    if (!DEBUG.enabled || !DEBUG[category]) return
    
    const icons = {
      info: 'ℹ️',
      success: '✅',
      error: '❌',
      debug: '🔍'
    }
    
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0]
    console.log(`[Formify ${timestamp}] ${icons[type]} [${category}] ${message}`, data || '')
  },

  info: (category, message, data = null) => {
    Logger._log('info', category, message, data)
  },
  
  success: (category, message, data = null) => {
    Logger._log('success', category, message, data)
  },
  
  error: (message, error) => {
    // 错误日志始终输出
    console.error(`[Formify] ❌ ${message}`, error)
  },
  
  debug: (category, message, data = null) => {
    Logger._log('debug', category, message, data)
  }
}

// 添加邮件客户端配置
const emailClients = {
  gmail: {
    recipient: 'input.agP.aFw[role="combobox"], div[role="textbox"][aria-label*="To"], div[role="textbox"][aria-label*="收件人"]',
    subject: 'input.aoT[name="subjectbox"], input[aria-label*="Subject"], input[aria-label*="主题"]',
    body: 'div.Am.aiL.Al.editable.LW-avf[contenteditable="true"], div[role="textbox"][aria-label="Message Body"], div[role="textbox"][aria-label*="正文"]'
  },
  outlook: {
    recipient: 'div[role="textbox"].EditorClass, div[contenteditable="true"][aria-label*="To"], div[contenteditable="true"][aria-label*="收件人"]',
    subject: 'input.fui-Input__input.zrkM_, input[aria-label*="Add a subject"], input[aria-label*="主题"]',
    body: 'div[role="textbox"].DziEn, div[contenteditable="true"][aria-label*="Message body"], div[contenteditable="true"][aria-label*="邮件正文"]'
  }
} as const;

// 更新 detectFormFields 函数
function detectFormFields(): FormField[] {
  const formFields: FormField[] = [];
  
  Logger.debug('form', 'Starting form field detection');
  
  // 检测邮件客户端
  Object.entries(emailClients).forEach(([client, selectors]) => {
    Object.entries(selectors).forEach(([fieldType, selector]) => {
      const element = document.querySelector(selector);
      if (element) {
        formFields.push({
          element: element as HTMLElement,
          type: element.tagName.toLowerCase(),
          id: element.id,
          name: fieldType,
          placeholder: (element as HTMLInputElement).placeholder || '',
          label: fieldType.charAt(0).toUpperCase() + fieldType.slice(1)
        });
      }
    });
  });

  // 如果没有找到特定的邮件客户端字段，继续检查其他字段
  if (formFields.length === 0) {
    // 获取所有可能的表单元素
    const allTextboxes = document.querySelectorAll('[role="textbox"]');
    const allInputs = document.querySelectorAll('input:not([type="hidden"]):not([type="submit"]):not([type="button"]):not([type="reset"])');
    const allTextareas = document.querySelectorAll('textarea');
    const allSelects = document.querySelectorAll('select');
    const allContentEditables = document.querySelectorAll('[contenteditable="true"]');
    const allEditorFrames = document.querySelectorAll('iframe.cke_wysiwyg_frame, iframe.tox-edit-area__iframe');
    
    Logger.debug('form', 'Found base elements count', {
      textboxes: allTextboxes.length,
      inputs: allInputs.length,
      textareas: allTextareas.length,
      selects: allSelects.length,
      contentEditables: allContentEditables.length,
      editorFrames: allEditorFrames.length
    });

    // GitHub 特定字段查询
    const githubTitleInput = document.querySelector('input[aria-label="Add a title"], input[aria-label="Title"]');
    const githubDescInput = document.querySelector(`
      textarea.MarkdownInput-module__textArea--QjIwG,
      textarea[name="description"],
      textarea[name="comment[body]"],
      .comment-form-textarea
    `);
    const githubTaskList = document.querySelector('.task-list-item, .contains-task-list');

    if (githubTitleInput || githubDescInput || githubTaskList) {
      if (githubTitleInput) {
        formFields.push({
          element: githubTitleInput as HTMLElement,
          type: 'text',
          id: githubTitleInput.id,
          name: 'title',
          placeholder: githubTitleInput.getAttribute('placeholder') || '',
          label: githubTitleInput.getAttribute('aria-label') || 'Title'
        });
      }
      if (githubDescInput) {
        formFields.push({
          element: githubDescInput as HTMLElement,
          type: 'textarea',
          id: githubDescInput.id,
          name: 'description',
          placeholder: githubDescInput.getAttribute('placeholder') || '',
          label: 'Description'
        });
      }
      if (githubTaskList) {
        formFields.push({
          element: githubTaskList as HTMLElement,
          type: 'tasklist',
          id: githubTaskList.id,
          name: 'tasklist',
          placeholder: '',
          label: 'Task List'
        });
      }
    }

    // 通用字段处理
    const commonInputs = Array.from(allInputs).filter(input => {
      const type = input.getAttribute('type');
      return type === 'text' || type === 'textarea' || type === 'email' || 
             type === 'tel' || type === 'url' || type === 'number' || 
             type === 'date' || type === 'search' || !type;
    });

    // 处理通用输入字段
    commonInputs.forEach(input => {
      const isDuplicate = formFields.some(field => field.element === input);
      if (!isDuplicate && isVisibleElement(input as HTMLElement)) {
        const label = findLabel(input as HTMLElement);
        formFields.push({
          element: input as HTMLElement,
          type: input.getAttribute('type') || 'text',
          id: input.id,
          name: input.getAttribute('name') || input.id || getNameFromAttributes(input),
          placeholder: input.getAttribute('placeholder') || '',
          label
        });
      }
    });

    // 处理 textarea 元素
    Array.from(allTextareas).forEach(textarea => {
      const isDuplicate = formFields.some(field => field.element === textarea);
      if (!isDuplicate && isVisibleElement(textarea as HTMLElement)) {
        const label = findLabel(textarea as HTMLElement);
        formFields.push({
          element: textarea as HTMLElement,
          type: 'textarea',
          id: textarea.id,
          name: textarea.getAttribute('name') || textarea.id || getNameFromAttributes(textarea),
          placeholder: textarea.getAttribute('placeholder') || '',
          label
        });
      }
    });

    // 处理 select 元素
    Array.from(allSelects).forEach(select => {
      const isDuplicate = formFields.some(field => field.element === select);
      if (!isDuplicate && isVisibleElement(select as HTMLElement)) {
        const label = findLabel(select as HTMLElement);
        formFields.push({
          element: select as HTMLElement,
          type: 'select',
          id: select.id,
          name: select.getAttribute('name') || select.id || getNameFromAttributes(select),
          placeholder: '',
          label
        });
      }
    });

    // 处理 contenteditable 元素
    Array.from(allContentEditables).forEach(element => {
      const isDuplicate = formFields.some(field => field.element === element);
      if (!isDuplicate && isVisibleElement(element as HTMLElement)) {
        const label = findLabel(element as HTMLElement);
        formFields.push({
          element: element as HTMLElement,
          type: 'contenteditable',
          id: element.id,
          name: element.getAttribute('name') || element.id || getNameFromAttributes(element),
          placeholder: '',
          label
        });
      }
    });

    // 处理编辑器 iframe
    Array.from(allEditorFrames).forEach(iframe => {
      const isDuplicate = formFields.some(field => field.element === iframe);
      if (!isDuplicate && isVisibleElement(iframe as HTMLElement)) {
        const label = findLabel(iframe as HTMLElement);
        formFields.push({
          element: iframe as HTMLElement,
          type: 'iframe-editor',
          id: iframe.id,
          name: iframe.getAttribute('name') || iframe.id || 'editor',
          placeholder: '',
          label: label || 'Editor'
        });
      }
    });
  }

  Logger.debug('form', 'Detected form fields:', formFields);
  return formFields;
}

// 检查元素是否可见
function isVisibleElement(element: HTMLElement): boolean {
  const style = window.getComputedStyle(element);
  return style.display !== 'none' && 
         style.visibility !== 'hidden' && 
         style.opacity !== '0' &&
         element.offsetWidth > 0 && 
         element.offsetHeight > 0;
}

// 从其他属性中获取可能的名称
function getNameFromAttributes(element: Element): string {
  // 尝试从各种属性中获取名称
  return element.getAttribute('aria-label') || 
         element.getAttribute('data-name') || 
         element.getAttribute('data-field-name') || 
         element.getAttribute('data-testid') || 
         element.getAttribute('title') || 
         '';
}

// Label finding helper function
function findLabel(input: HTMLElement): string {
  // Try to find label by for attribute
  if (input.id) {
    const label = document.querySelector(`label[for="${input.id}"]`)
    if (label) return label.textContent?.trim() || ''
  }

  // Try to find parent label
  const parentLabel = input.closest('label')
  if (parentLabel) return parentLabel.textContent?.trim() || ''

  // Try to find nearby text that might be a label
  const previousElement = input.previousElementSibling
  if (previousElement && previousElement.tagName !== 'INPUT' && previousElement.tagName !== 'TEXTAREA') {
    return previousElement.textContent?.trim() || ''
  }

  return ''
}

// 添加一个全局变量来跟踪样式标签
let generatingStyleTag: HTMLStyleElement | null = null

// 显示生成中的视觉效果
function showGeneratingEffect() {
  try {
    // 如果已经有样式标签，先移除
    if (generatingStyleTag && generatingStyleTag.parentNode) {
      generatingStyleTag.parentNode.removeChild(generatingStyleTag)
    }
    
    // 检测表单字段
    const formFields = detectFormFields()
    if (!formFields.length) {
      Logger.debug('effect', 'No form fields detected for showing effect')
      return
    }
    
    // 添加加载动画样式
    generatingStyleTag = document.createElement('style')
    generatingStyleTag.textContent = `
      @keyframes formifyBorderGlow {
        0% { outline: 2px solid rgba(33, 150, 243, 0.4); box-shadow: 0 0 5px rgba(33, 150, 243, 0.4); }
        50% { outline: 2px solid rgba(33, 150, 243, 0.8); box-shadow: 0 0 15px rgba(33, 150, 243, 0.6); }
        100% { outline: 2px solid rgba(33, 150, 243, 0.4); box-shadow: 0 0 5px rgba(33, 150, 243, 0.4); }
      }
      .formify-loading {
        animation: formifyBorderGlow 1.5s ease-in-out infinite !important;
        z-index: 9999;
        position: relative;
      }
    `
    document.head.appendChild(generatingStyleTag)
    
    // 给所有表单字段添加加载动画
    formFields.forEach(field => {
      if (field.element) {
        field.element.classList.add('formify-loading')
      }
    })
    
    Logger.debug('effect', 'Added generating effect to form fields')
  } catch (error) {
    Logger.error('Error showing generating effect:', error)
  }
}

// 移除生成中的视觉效果
function removeGeneratingEffect() {
  try {
    // 移除所有加载动画
    document.querySelectorAll('.formify-loading').forEach(el => {
      el.classList.remove('formify-loading')
    })
    
    // 移除样式标签
    if (generatingStyleTag && generatingStyleTag.parentNode) {
      generatingStyleTag.parentNode.removeChild(generatingStyleTag)
      generatingStyleTag = null
    }
    
    Logger.debug('effect', 'Removed generating effect from form fields')
  } catch (error) {
    Logger.error('Error removing generating effect:', error)
  }
}

// 更新 fillForm 函数
function fillForm(formContent: FormContent, callback?: FillFormCallback): void {
  try {
    Logger.debug('fill', 'Starting form fill with content:', formContent)
    
    // 检查是否需要解析 JSON
    let contentToUse = formContent
    if (typeof formContent === 'object' && formContent.content && typeof formContent.content === 'string') {
      try {
        const parsedContent = JSON.parse(formContent.content)
        Logger.debug('fill', 'Successfully parsed JSON content:', parsedContent)
        contentToUse = parsedContent
      } catch (e) {
        Logger.error('Failed to parse JSON content:', e)
        // 如果解析失败，继续使用原始内容
      }
    }
    
    Logger.debug('fill', 'Form content structure:', {
      hasContent: !!contentToUse,
      contentType: typeof contentToUse,
      keys: contentToUse ? Object.keys(contentToUse) : [],
      values: contentToUse ? Object.entries(contentToUse).map(([key, value]) => ({
        key,
        type: typeof value,
        hasValue: !!value
      })) : []
    })
    
    // 移除生成中的视觉效果
    removeGeneratingEffect()
    
    // 检测表单字段
    const formFields = detectFormFields()
    if (!formFields.length) {
      throw new Error('No form fields detected')
    }

    // 先处理主题字段
    const subjectField = formFields.find(field => field.name === 'subject')
    if (subjectField && subjectField.element && contentToUse.subject) {
      Logger.debug('fill', 'Processing subject field...', { value: contentToUse.subject })
      try {
        // 检查是否是 React 组件
        const isReactComponent = subjectField.element.classList.contains('fui-Input__input') || 
                               subjectField.element.hasAttribute('data-reactid') ||
                               !!subjectField.element.closest('[data-reactroot]');

        if (isReactComponent) {
          // 使用 React 组件的处理方式
          if (subjectField.element instanceof HTMLInputElement) {
            const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
              window.HTMLInputElement.prototype,
              "value"
            )?.set;
            
            if (nativeInputValueSetter) {
              // 调用setter设置值（忽略返回值）
              void nativeInputValueSetter.call(subjectField.element, contentToUse.subject);
              
              // 触发一系列事件来确保 React 状态更新
              ['input', 'change', 'blur'].forEach(eventType => {
                const event = new Event(eventType, {
                  bubbles: true,
                  composed: true,
                  cancelable: true
                })
                subjectField.element.dispatchEvent(event)
              });
              
              // 确保元素获得焦点
              subjectField.element.focus()
              
              // 创建并触发输入事件
              const inputEvent = new InputEvent('input', {
                bubbles: true,
                cancelable: true,
                inputType: 'insertText',
                data: contentToUse.subject
              })
              subjectField.element.dispatchEvent(inputEvent)
              
              // 最后失去焦点
              subjectField.element.blur()
              
              Logger.debug('fill', 'Filled React input component')
            }
          }
        } else {
          // 如果不是 React 组件，使用普通方式
          fillFieldContent(subjectField, contentToUse.subject)
        }
      } catch (e) {
        Logger.error('Error filling subject field:', e)
      }
    }

    // 处理其他字段
    formFields.forEach(field => {
      // 跳过主题字段，因为已经处理过了
      if (field.name === 'subject') return
      
      try {
        let content: string | undefined
        
        if (field.name === 'to' || field.name === 'recipient') {
          content = contentToUse.to || contentToUse.recipient
          Logger.debug('fill', `Matching recipient field: ${field.name}`, { content })
        } else if (field.name === 'body' || field.name === 'content' || field.name === 'editor') {
          content = contentToUse[''] || contentToUse.body || contentToUse.content || contentToUse.message
          Logger.debug('fill', `Matching body field: ${field.name}`, { content })
        } else if (field.name === 'title' || field.name === 'description') {
          content = contentToUse[field.name]
        } else {
          // 更通用的内容匹配逻辑
          const labelNoAsterisk = field.label?.replace(/\s*\*\s*$/, '') || ''
          const labelFirstWord = field.label?.split(/[\s*]/)[0] || ''
          
          // 创建一个根据字段语言特性进行匹配的函数
          const findValueByLanguage = (obj: any, key: string): string | undefined => {
            if (!key) return undefined
            
            // 直接匹配
            if (obj[key] !== undefined) return obj[key]
            
            // 不区分大小写匹配
            const lowerKey = key.toLowerCase()
            const keyMatch = Object.keys(obj).find(k => k.toLowerCase() === lowerKey)
            if (keyMatch) return obj[keyMatch]
            
            // 部分匹配
            const partialMatch = Object.keys(obj).find(k => 
              k.toLowerCase().includes(lowerKey) || lowerKey.includes(k.toLowerCase())
            )
            if (partialMatch) return obj[partialMatch]
            
            // 尝试智能匹配常见字段
            const commonFieldMappings: Record<string, string[]> = {
              'name': ['name', 'fullname', 'full_name', 'full-name', 'username', 'user', 'firstname', 'first_name', 'first-name'],
              'email': ['email', 'email_address', 'email-address', 'mail'],
              'phone': ['phone', 'telephone', 'tel', 'mobile', 'cell', 'phone_number', 'phone-number'],
              'address': ['address', 'street', 'location'],
              'city': ['city', 'town'],
              'state': ['state', 'province', 'region'],
              'country': ['country', 'nation'],
              'zip': ['zip', 'zipcode', 'postal', 'postal_code', 'postal-code'],
              'company': ['company', 'organization', 'organisation', 'employer'],
              'website': ['website', 'site', 'url', 'web', 'homepage'],
              'message': ['message', 'comment', 'feedback', 'note', 'description', 'details']
            }
            
            for (const [category, synonyms] of Object.entries(commonFieldMappings)) {
              if (synonyms.some(s => lowerKey.includes(s) || s.includes(lowerKey))) {
                const categoryMatch = Object.keys(obj).find(k => 
                  commonFieldMappings[category]?.some(s => k.toLowerCase().includes(s))
                )
                if (categoryMatch) return obj[categoryMatch]
              }
            }
            
            return undefined
          }
          
          // 尝试多种匹配方式
          content = findValueByLanguage(contentToUse, field.name) || 
                   findValueByLanguage(contentToUse, field.id) || 
                   findValueByLanguage(contentToUse, labelNoAsterisk) || 
                   findValueByLanguage(contentToUse, labelFirstWord) || 
                   findValueByLanguage(contentToUse, field.placeholder || '')
          
          Logger.debug('fill', `Matching field: ${field.name}`, { 
            label: field.label, 
            content, 
            matchAttempts: [field.name, field.id, labelNoAsterisk, labelFirstWord, field.placeholder] 
          })
        }
        
        if (content) {
          fillFieldContent(field, content)
        } else {
          // 如果没有找到匹配的内容，只移除加载动画
        }
      } catch (e) {
        Logger.error(`Error filling field ${field.name}:`, e)
      }
    })
    
    // 回调处理
    if (callback) {
      callback.success?.()
    }
  } catch (error) {
    Logger.error('Error filling form:', error)
    
    // 回调处理
    if (callback) {
      callback.error?.(error)
    }
  }
}

// 更新 fillFieldContent 函数
function fillFieldContent(field: FormField, content: string): void {
  if (!field.element || !content) return;
  
  try {
    Logger.debug('fill', `Filling field: ${field.name}`, { content });
    
    // 检查是否是 Outlook 的 contenteditable 元素
    const isOutlookContentEditable = field.element.getAttribute('contenteditable') === 'true' && 
                                   field.element.getAttribute('role') === 'textbox' &&
                                   (field.name === 'body' || field.name === 'to');
    
    // 检查是否是 Gmail 的 contenteditable 元素
    const isGmailContentEditable = (field.element.getAttribute('contenteditable') === 'true' &&
                             (field.element.classList.contains('editable') || 
                              field.element.classList.contains('LW-avf') ||
                              field.element.closest('.Am.Al.editable') !== null)) ||
                             (field.element.getAttribute('role') === 'textbox' &&
                              field.element.closest('[aria-label="Message Body"]') !== null);
    
    // 检查是否是 GitHub 的特定组件
    const isGitHubComponent = field.element.classList.contains('MarkdownInput-module__textArea--QjIwG') ||
                            field.element.classList.contains('comment-form-textarea') ||
                            field.element.classList.contains('js-comment-field') ||
                            field.element.getAttribute('aria-label') === 'Add a title' ||
                            field.element.getAttribute('aria-label') === 'Title' ||
                            field.element.getAttribute('name') === 'issue[title]' ||
                            field.element.getAttribute('name') === 'issue[body]' ||
                            field.element.getAttribute('name') === 'pull_request[title]' ||
                            field.element.getAttribute('name') === 'pull_request[body]' ||
                            field.element.getAttribute('name') === 'comment[body]';
    
    // 检查是否是 React 组件
    const isReactComponent = !isGitHubComponent && (field.element.classList.contains('fui-Input__input') || 
                           field.element.hasAttribute('data-reactid') ||
                           !!field.element.closest('[data-reactroot]'));
    
    // 检查是否是富文本编辑器
    const isRichTextEditor = field.element.getAttribute('contenteditable') === 'true' ||
                             field.element.classList.contains('ck-editor__editable') ||
                             field.element.classList.contains('tox-edit-area__iframe') ||
                             field.element.classList.contains('mce-content-body');
    
    // 检查是否是 iframe 编辑器
    const isIframeEditor = field.element.tagName === 'IFRAME' && 
                          (field.element.classList.contains('cke_wysiwyg_frame') ||
                           field.element.classList.contains('tox-edit-area__iframe'));
    
    // 处理 iframe 编辑器
    if (isIframeEditor) {
      try {
        const iframe = field.element as HTMLIFrameElement;
        const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document;
        
        if (iframeDocument && iframeDocument.body) {
          iframeDocument.body.innerHTML = content;
          
          // 触发 iframe 内容变化事件
          const event = new Event('input', { bubbles: true });
          iframeDocument.body.dispatchEvent(event);
          
          Logger.debug('fill', 'Filled iframe editor content');
        }
      } catch (e) {
        Logger.error('Error filling iframe editor:', e);
      }
      return;
    }
    
    // 处理富文本编辑器
    if (isRichTextEditor) {
      field.element.innerHTML = content;
      
      // 触发内容变化事件
      ['input', 'change'].forEach(eventType => {
        const event = new Event(eventType, { bubbles: true });
        field.element.dispatchEvent(event);
      });
      
      Logger.debug('fill', 'Filled rich text editor content');
      return;
    }
    
    // 处理 Gmail contenteditable 元素
    if (isGmailContentEditable) {
      try {
        // 聚焦元素
        field.element.focus();
        
        // 清空现有内容
        field.element.innerHTML = '';
        
        // 创建并插入文本节点
        const textNode = document.createTextNode(content);
        field.element.appendChild(textNode);
        
        // 触发必要的事件，使用延迟确保DOM更新完成
        setTimeout(() => {
          ['input', 'change', 'keydown', 'keyup', 'blur'].forEach(eventType => {
            const event = new Event(eventType, {
              bubbles: true,
              composed: true,
              cancelable: true
            });
            field.element.dispatchEvent(event);
          });
          
          // 模拟输入事件
          const inputEvent = new InputEvent('input', {
            bubbles: true,
            cancelable: true,
            inputType: 'insertText',
            data: content
          });
          field.element.dispatchEvent(inputEvent);
        }, 50);
        
        Logger.debug('fill', 'Filled Gmail contenteditable element');
        return;
      } catch (e) {
        Logger.error('Failed to set Gmail contenteditable element value:', e);
      }
    }
    
    // 处理 Outlook contenteditable 元素
    if (isOutlookContentEditable) {
      try {
        // 聚焦元素
        field.element.focus();
        
        // 清空现有内容
        field.element.innerHTML = '';
        
        // 创建并插入文本节点
        const textNode = document.createTextNode(content);
        field.element.appendChild(textNode);
        
        // 触发必要的事件，使用延迟确保DOM更新完成
        setTimeout(() => {
          ['input', 'change', 'keydown', 'keyup', 'blur'].forEach(eventType => {
            const event = new Event(eventType, { 
              bubbles: true, 
              composed: true,
              cancelable: true 
            });
            field.element.dispatchEvent(event);
          });
          
          // 模拟输入事件
          const inputEvent = new InputEvent('input', {
            bubbles: true,
            cancelable: true,
            inputType: 'insertText',
            data: content
          });
          field.element.dispatchEvent(inputEvent);
          
          // 如果是收件人字段，额外处理
          if (field.name === 'to' || field.name === 'recipient') {
            // 模拟按下回车键
            const enterEvent = new KeyboardEvent('keydown', {
              key: 'Enter',
              code: 'Enter',
              keyCode: 13,
              which: 13,
              bubbles: true,
              cancelable: true
            });
            field.element.dispatchEvent(enterEvent);
          }
        }, 50);
        
        Logger.debug('fill', 'Filled Outlook contenteditable element');
        return;
      } catch (e) {
        Logger.error('Failed to set Outlook contenteditable element value:', e);
      }
    }
    
    // 处理 React 组件
    if (isReactComponent) {
      if (field.element instanceof HTMLInputElement || field.element instanceof HTMLTextAreaElement) {
        const nativeValueSetter = Object.getOwnPropertyDescriptor(
          field.element instanceof HTMLInputElement ? window.HTMLInputElement.prototype : window.HTMLTextAreaElement.prototype,
          "value"
        )?.set;
        
        if (nativeValueSetter) {
          nativeValueSetter.call(field.element, content);
          
          // 触发一系列事件来确保 React 状态更新
          ['input', 'change', 'blur'].forEach(eventType => {
            const event = new Event(eventType, {
              bubbles: true,
              composed: true,
              cancelable: true
            });
            field.element.dispatchEvent(event);
          });
          
          // 确保元素获得焦点
          field.element.focus();
          
          // 创建并触发输入事件
          const inputEvent = new InputEvent('input', {
            bubbles: true,
            cancelable: true,
            inputType: 'insertText',
            data: content
          });
          field.element.dispatchEvent(inputEvent);
          
          // 最后失去焦点
          field.element.blur();
          
          Logger.debug('fill', 'Filled React input component');
        }
      } else if (field.element instanceof HTMLSelectElement) {
        fillSelectElement(field.element, content);
      }
      return;
    } else if (isGitHubComponent) {
      // GitHub 特定处理
      if (field.element instanceof HTMLTextAreaElement) {
        try {
          // 先聚焦元素
          field.element.focus();
          
          const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLTextAreaElement.prototype, 'value')?.set;
          if (nativeInputValueSetter) {
            nativeInputValueSetter.call(field.element, content);
          } else {
            field.element.value = content;
          }
          
          // 使用延迟确保DOM更新完成
          setTimeout(() => {
            // 触发必要的事件来确保 GitHub 的表单状态更新
            ['input', 'change', 'keydown', 'keyup', 'blur'].forEach(eventType => {
              const event = new Event(eventType, { 
                bubbles: true,
                composed: true,
                cancelable: true 
              });
              field.element.dispatchEvent(event);
            });
            
            // 模拟输入事件
            const inputEvent = new InputEvent('input', {
              bubbles: true,
              cancelable: true,
              inputType: 'insertText',
              data: content
            });
            field.element.dispatchEvent(inputEvent);
          }, 50);
        } catch (e) {
          field.element.value = content;
        }
      } else if (field.element instanceof HTMLInputElement) {
        try {
          // 先聚焦元素
          field.element.focus();
          
          const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, 'value')?.set;
          if (nativeInputValueSetter) {
            nativeInputValueSetter.call(field.element, content);
          } else {
            field.element.value = content;
          }
          
          // 使用延迟确保DOM更新完成
          setTimeout(() => {
            // 触发必要的事件来确保 GitHub 的表单状态更新
            ['input', 'change', 'keydown', 'keyup', 'blur'].forEach(eventType => {
              const event = new Event(eventType, { 
                bubbles: true,
                composed: true,
                cancelable: true 
              });
              field.element.dispatchEvent(event);
            });
            
            // 模拟输入事件
            const inputEvent = new InputEvent('input', {
              bubbles: true,
              cancelable: true,
              inputType: 'insertText',
              data: content
            });
            field.element.dispatchEvent(inputEvent);
          }, 50);
        } catch (e) {
          field.element.value = content;
        }
      } else {
        field.element.textContent = content;
        
        // 使用延迟确保DOM更新完成
        setTimeout(() => {
          // 触发必要的事件
          ['input', 'change', 'blur'].forEach(eventType => {
            const event = new Event(eventType, { bubbles: true });
            field.element.dispatchEvent(event);
          });
        }, 50);
      }
      
      return;
    }
    
    // 处理普通 HTML 元素
    if (field.element instanceof HTMLInputElement) {
      const input = field.element;
      
      // 根据输入类型进行处理
      switch (input.type.toLowerCase()) {
        case 'checkbox':
          // 根据内容决定是否选中
          const shouldCheck = /^(yes|true|1|on|checked|selected|enable|enabled)$/i.test(content.trim());
          input.checked = shouldCheck;
          break;
        
        case 'radio':
          // 只有当内容与值匹配时才选中
          if (input.value.toLowerCase() === content.toLowerCase()) {
            input.checked = true;
          } else {
            // 尝试查找同名的其他单选按钮
            const radioGroup = document.querySelectorAll(`input[type="radio"][name="${input.name}"]`);
            for (const radio of Array.from(radioGroup)) {
              if ((radio as HTMLInputElement).value.toLowerCase() === content.toLowerCase()) {
                (radio as HTMLInputElement).checked = true;
                break;
              }
            }
          }
          break;
        
        case 'date':
          // 尝试解析日期
          try {
            const date = new Date(content);
            if (!isNaN(date.getTime())) {
              // 格式化为 YYYY-MM-DD
              const year = date.getFullYear();
              const month = String(date.getMonth() + 1).padStart(2, '0');
              const day = String(date.getDate()).padStart(2, '0');
              input.value = `${year}-${month}-${day}`;
            }
          } catch (e) {
            // 如果无法解析，使用原始内容
            input.value = content;
          }
          break;
        
        default:
          // 对于其他类型的输入框，直接设置值
          input.value = content;
      }
      
      // 触发事件
      ['input', 'change'].forEach(eventType => {
        const event = new Event(eventType, { bubbles: true });
        input.dispatchEvent(event);
      });
      
    } else if (field.element instanceof HTMLTextAreaElement) {
      const textarea = field.element;
      textarea.value = content;
      
      // 触发事件
      ['input', 'change'].forEach(eventType => {
        const event = new Event(eventType, { bubbles: true });
        textarea.dispatchEvent(event);
      });
      
    } else if (field.element instanceof HTMLSelectElement) {
      fillSelectElement(field.element, content);
    } else {
      // 对于其他类型的元素，尝试设置 textContent
      field.element.textContent = content;
      
      // 触发通用事件
      ['input', 'change'].forEach(eventType => {
        const event = new Event(eventType, { bubbles: true });
        field.element.dispatchEvent(event);
      });
    }
    
    Logger.debug('fill', `Successfully filled field: ${field.name}`);
  } catch (error) {
    Logger.error(`Error filling field ${field.name}:`, error);
  }
}

// 辅助函数：填充 select 元素
function fillSelectElement(select: HTMLSelectElement, content: string): void {
  // 尝试多种方式匹配选项
  const contentLower = content.toLowerCase().trim();
  const options = Array.from(select.options);
  
  // 1. 尝试完全匹配值
  let matchedOption = options.find(option => option.value === content);
  
  // 2. 尝试完全匹配文本
  if (!matchedOption) {
    matchedOption = options.find(option => option.text === content);
  }
  
  // 3. 尝试不区分大小写匹配
  if (!matchedOption) {
    matchedOption = options.find(option => 
      option.value.toLowerCase() === contentLower || 
      option.text.toLowerCase() === contentLower
    );
  }
  
  // 4. 尝试部分匹配
  if (!matchedOption) {
    matchedOption = options.find(option => 
      option.value.toLowerCase().includes(contentLower) || 
      contentLower.includes(option.value.toLowerCase()) ||
      option.text.toLowerCase().includes(contentLower) || 
      contentLower.includes(option.text.toLowerCase())
    );
  }
  
  // 如果找到匹配项，设置选中
  if (matchedOption) {
    select.value = matchedOption.value;
    
    // 触发 change 事件
    const event = new Event('change', { bubbles: true });
    select.dispatchEvent(event);
    
    Logger.debug('fill', `Selected option: ${matchedOption.text} (${matchedOption.value})`);
  } else {
    Logger.debug('fill', `No matching option found for: ${content}`);
  }
}

// 处理未捕获的 Promise 错误
window.addEventListener('unhandledrejection', function(event) {
  if (errorMessages.some(msg => event.reason?.message?.includes(msg))) {
    event.preventDefault()
    event.stopPropagation()
  }
})

// 处理常规错误
window.addEventListener('error', function(event) {
  if (errorMessages.some(msg => event.error?.message?.includes(msg))) {
    event.preventDefault()
    event.stopPropagation()
  }
}, true)

// 更新 checkPageStatus 函数
function checkPageStatus(): { 
  isValid: boolean;
  needsRefresh: boolean;
  hasFormFields: boolean;
  pageType?: string;
} {
  try {
    const isLoaded = document.readyState === 'complete';
    
    // 检查特定网站
    const isGmail = window.location.hostname.includes('mail.google.com');
    const isOutlook = window.location.hostname.includes('outlook.live.com') || 
                     window.location.hostname.includes('outlook.office.com');
    const isGitHub = window.location.hostname.includes('github.com');
    
    let pageType = 'unknown';
    if (isGmail) pageType = 'gmail';
    else if (isOutlook) pageType = 'outlook';
    else if (isGitHub) pageType = 'github';
    
    // 特定网站的加载检查
    let isLoading = false;
    
    if (isGmail) {
      // Gmail 特定检查
      const mainContent = document.querySelector('div[role="main"]');
      const composeButton = document.querySelector('[gh="cm"]'); // 撰写邮件按钮
      isLoading = !mainContent || !composeButton;
    } else if (isOutlook) {
      // Outlook 特定检查
      const mainContent = document.querySelector('div[role="main"]');
      const newMailButton = document.querySelector('[aria-label*="New mail"], [aria-label*="新邮件"]');
      isLoading = !mainContent || !newMailButton;
    } else if (isGitHub) {
      // GitHub 特定检查
      isLoading = !!document.querySelector('.is-loading');
    } else {
      // 通用检查
      isLoading = document.documentElement.classList.contains('loading') ||
                  document.body.classList.contains('loading');
    }

    // 检测表单字段
    const formFields = detectFormFields();
    const hasFields = formFields.length > 0;
    
    // 如果页面已加载完成且找到了表单字段，就不需要刷新
    const needsRefresh = !isLoaded || (isLoading && !hasFields);
    
    return {
      isValid: true,
      needsRefresh,
      hasFormFields: hasFields,
      pageType
    };
  } catch (error) {
    Logger.error('Error checking page status:', error);
    return {
      isValid: false,
      needsRefresh: true,
      hasFormFields: false
    };
  }
}

// 修改 setupMessageListener 函数
function setupMessageListener() {
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    try {
      Logger.debug('message', 'Received message:', message);
      
      switch (message.action || message.type) {
        case 'fillForm':
          Logger.debug('fill', 'Received fillForm message with data:', message.data);
          // 确保我们传递正确的数据结构
          const formContentToFill = message.data.data || message.data;
          fillForm(formContentToFill, {
            success: () => {
              Logger.success('fill', 'Form filled successfully');
              sendResponse({ success: true });
            },
            error: (error) => {
              Logger.error('Failed to fill form:', error);
              sendResponse({ success: false, error: error instanceof Error ? error.message : String(error) });
            }
          });
          return true;
        
        case 'showGeneratingEffect':
          Logger.debug('effect', 'Received showGeneratingEffect message');
          showGeneratingEffect();
          sendResponse({ success: true });
          return true;
        
        case 'checkFormFields':
          const formFields = detectFormFields();
          Logger.debug('form', 'Checking form fields, found:', formFields.length);
          sendResponse({
            hasFields: formFields.length > 0,
            fieldCount: formFields.length
          });
          return true;
          
        case 'checkPageStatus':
          const status = checkPageStatus();
          Logger.debug('form', 'Checking page status:', status);
          sendResponse(status);
          return true;

        case 'getFormFields':
          const fields = detectFormFields();
          Logger.debug('form', 'Getting form fields, found:', fields.length);
          sendResponse({
            success: true,
            fields: fields.map(field => ({
              type: field.type,
              id: field.id,
              name: field.name,
              placeholder: field.placeholder,
              label: field.label
            }))
          });
          return true;

        default:
          Logger.error('Unknown message type:', message);
          sendResponse({ success: false, error: 'Unknown message type' });
          return true;
      }
    } catch (error) {
      Logger.error('Error handling message:', error);
      sendResponse({ 
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred'
      });
      return true;
    }
  });
  
  // 发送初始化成功消息
  chrome.runtime.sendMessage({ 
    type: 'contentScriptInitialized',
    url: window.location.href 
  });
}
