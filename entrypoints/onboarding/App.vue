<template>
  <div class="container">
    <!-- Step 1: Welcome -->
    <div class="step" :class="{ active: currentStep === 1 }" data-step="1">
      <div class="logo-container">
        <svg class="login-logo" width="1024" height="1024" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M814.311 75H125C97.3858 75 75 97.3858 75 125V918C75 945.614 97.3858 968 125 968H905.481C933.096 968 955.481 945.614 955.481 918V216.17L814.311 75Z" fill="#1D5DF4"/>
          <g style="mix-blend-mode:hard-light">
            <path d="M956 217H814V75L885 146L956 217Z" fill="#D9D9D9"/>
          </g>
          <rect x="340.211" y="344.847" width="504.457" height="81.6033" fill="white"/>
          <rect x="340.211" y="508.054" width="504.457" height="81.6033" fill="white"/>
          <rect x="340.211" y="671.261" width="504.457" height="81.6033" fill="white"/>
          <path d="M245.625 333.72L260.152 372.977L299.409 387.504L260.152 402.03L245.625 441.288L231.099 402.03L191.841 387.504L231.099 372.977L245.625 333.72Z" fill="white"/>
          <path d="M245.625 496.926L260.152 536.184L299.409 550.71L260.152 565.237L245.625 604.494L231.099 565.237L191.841 550.71L231.099 536.184L245.625 496.926Z" fill="white"/>
          <path d="M245.625 660.133L260.152 699.39L299.409 713.917L260.152 728.443L245.625 767.701L231.099 728.443L191.841 713.917L231.099 699.39L245.625 660.133Z" fill="white"/>
        </svg>
      </div>
      <h1>Welcome to Fillify</h1>
      <p class="subtitle">
        Let's get you started with Fillify - your AI-powered form filling assistant.<br>
        We'll help you set up everything in just a few steps.
      </p>
    </div>

    <!-- Step 2: API Key Setup -->
    <div class="step" :class="{ active: currentStep === 2 }" data-step="2">
      <h1>Set Up Your API Key</h1>
      <p class="subtitle">
        Choose your preferred AI provider and set up the API key.<br>
        You can also set this up later in the extension settings.
      </p>
      <div class="highlight-box">
        <div class="provider-select-wrapper">
          <select v-model="selectedProvider" class="provider-select" @change="handleProviderChange">
            <option value="">Select AI Provider</option>
            <option v-for="(link, provider) in providerLinks" :key="provider" :value="provider">
              {{ provider.charAt(0).toUpperCase() + provider.slice(1) }}
            </option>
          </select>
        </div>
        <div id="apiKeyInputWrapper" v-show="selectedProvider">
          <div class="key-input">
            <input
              :type="showPassword ? 'text' : 'password'"
              v-model="apiKey"
              class="api-key-input"
              :placeholder="`Enter your ${selectedProvider} API key`"
            >
            <button class="toggle-visibility" @click="toggleVisibility">
              <svg class="visibility-icon" viewBox="0 0 24 24" width="20" height="20">
                <path :d="visibilityPath"/>
              </svg>
            </button>
          </div>
          <div class="api-key-help">
            <a :href="selectedProvider ? providerLinks[selectedProvider] : '#'" target="_blank">Get API Key</a>
          </div>
        </div>
        <div class="setup-later">
          <button class="setup-later-btn" @click="handleSetupLater">Set up later</button>
        </div>
      </div>
    </div>

    <!-- Step 3: Completion -->
    <div class="step" :class="{ active: currentStep === 3 }" data-step="3">
      <h1>You're All Set!</h1>
      <p class="subtitle">
        Great! You've completed the setup.<br>
        Now you can start using Fillify to fill forms intelligently.
      </p>
      <div class="highlight-box">
        <h2 class="quick-tips-title">Quick Tips:</h2>
        <ul class="quick-tips-list">
          <li>Click the Fillify icon in your toolbar to start</li>
          <li>Choose between Bug Report or General mode</li>
          <li>Describe what you want, and let AI do the rest</li>
        </ul>
      </div>
    </div>

    <!-- Navigation -->
    <div class="navigation">
      <button
        class="nav-button back-button"
        v-show="currentStep > 1"
        @click="handleBack"
      >
        Back
      </button>
      <button
        class="nav-button next-button"
        @click="handleNext"
      >
        {{ currentStep === totalSteps ? 'Finish' : 'Next' }}
      </button>
    </div>

    <!-- Progress Dots -->
    <div class="progress-dots">
      <div
        v-for="n in totalSteps"
        :key="n"
        class="dot"
        :class="{ active: currentStep === n }"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Constants
const STORAGE_KEYS = {
  API_KEYS: 'formify_api_keys',
  SETTINGS: 'formify_settings',
  ONBOARDING_COMPLETED: 'formify_onboarding_completed',
  VALIDATED_KEYS: 'formify_validated_keys'
}

// 定义 Provider 类型
type Provider = 'openai' | 'moonshot' | 'claude' | 'gemini' | 'deepseek' | 'openrouter'

// State
const currentStep = ref(1)
const totalSteps = 3
const selectedProvider = ref<Provider | ''>('')
const apiKey = ref('')
const showPassword = ref(false)

// Provider Links
const providerLinks: Record<Provider, string> = {
  openai: 'https://platform.openai.com/api-keys',
  moonshot: 'https://platform.moonshot.cn/console/api-keys',
  claude: 'https://console.anthropic.com/account/keys',
  gemini: 'https://makersuite.google.com/app/apikey',
  deepseek: 'https://platform.deepseek.com/api_keys',
  openrouter: 'https://openrouter.ai/keys'
}

// Computed
const visibilityPath = computed(() => showPassword.value ?
  'M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z' :
  'M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7zM2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3 2 4.27zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2zm4.31-.78l3.15 3.15.02-.16c0-1.66-1.34-3-3-3l-.17.01z'
)

// Methods
const toggleVisibility = () => {
  showPassword.value = !showPassword.value
}

const handleProviderChange = () => {
  apiKey.value = ''
}

const handleSetupLater = () => {
  currentStep.value++
}

const handleBack = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

const handleNext = async () => {
  // 如果在 API Key 输入步骤
  if (currentStep.value === 2) {
    // 如果没有选择服务商
    if (!selectedProvider.value) {
      alert('Please select an AI provider or click "Set up later"')
      return
    }

    // 如果选择了提供商但没有输入 API key
    if (selectedProvider.value && !apiKey.value.trim()) {
      alert('Please enter your API key or click "Set up later"')
      return
    }

    // 如果有 API key，则保存
    if (selectedProvider.value && apiKey.value) {
      const saved = await saveApiKey(apiKey.value, selectedProvider.value)
      if (!saved) return
    }
  }

  // 如果是最后一步，完成设置
  if (currentStep.value === totalSteps) {
    await chrome.storage.sync.set({
      [STORAGE_KEYS.ONBOARDING_COMPLETED]: true
    })
    window.close()
    return
  }

  currentStep.value++
}

// 添加 API key 保存函数
const saveApiKey = async (key: string, provider: Provider): Promise<boolean> => {
  try {
    const storage = await chrome.storage.sync.get([STORAGE_KEYS.SETTINGS, STORAGE_KEYS.API_KEYS])
    const settings = storage[STORAGE_KEYS.SETTINGS] || {}
    const apiKeys = storage[STORAGE_KEYS.API_KEYS] || {}

    settings.useCustomApi = true
    settings.defaultProvider = provider
    apiKeys[provider] = key

    // Set default model based on provider
    switch (provider) {
      case 'openai':
        settings.defaultModel = 'gpt-3.5-turbo'
        break
      case 'moonshot':
        settings.defaultModel = 'moonshot-v1-8k'
        break
      case 'claude':
        settings.defaultModel = 'claude-3-sonnet-20240229'
        break
      case 'gemini':
        settings.defaultModel = 'gemini-2.0-flash'
        break
      case 'deepseek':
        settings.defaultModel = 'deepseek-chat'
        break
      case 'openrouter':
        settings.defaultModel = 'openai/gpt-3.5-turbo'
        break
    }

    await chrome.storage.sync.set({
      [STORAGE_KEYS.API_KEYS]: apiKeys,
      [STORAGE_KEYS.SETTINGS]: settings
    })

    return true
  } catch (error) {
    console.error('Error saving API key:', error)
    return false
  }
}

// ... 其他方法和样式保持不变
</script>

<style>
/* 基础样式 */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  margin: 0;
  padding: 0;
  background: #f5f5f5;
  color: #333;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px;
  text-align: center;
}

.step {
  display: none;
  animation: fadeIn 0.5s ease-out;
}

.step.active {
  display: block;
}

.logo {
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
}

h1 {
  font-size: 2.5em;
  color: #1D5DF4;
  margin-bottom: 10px;
}

.subtitle {
  font-size: 1.2em;
  color: #666;
  margin-bottom: 40px;
  line-height: 1.6;
}

.navigation {
  margin-top: 40px;
  display: flex;
  justify-content: center;
  gap: 20px;
}

.nav-button {
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.next-button {
  background: #1D5DF4;
  color: white;
}

.next-button:hover {
  background: #1850D8;
}

.back-button {
  background: #e0e0e0;
  color: #333;
}

.back-button:hover {
  background: #d0d0d0;
}

.progress-dots {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 30px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #e0e0e0;
  transition: all 0.3s;
}

.dot.active {
  background: #1D5DF4;
  transform: scale(1.2);
}

.highlight-box {
  background: white;
  padding: 40px;
  border-radius: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin: 20px auto;
  max-width: 480px;
  width: 100%;
  box-sizing: border-box;
}

/* API Key 输入相关样式 */
.provider-select-wrapper {
  margin-bottom: 20px;
  width: 100%;
}

.provider-select {
  width: 100%;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  font-size: 16px;
  background-color: white;
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 16px center;
  background-size: 16px;
  color: #333;
  box-sizing: border-box;
}

.key-input {
  position: relative;
  margin-top: 20px;
  width: 100%;
}

.api-key-input {
  width: 100%;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  font-size: 16px;
  background: white;
  color: #333;
  padding-right: 40px;
  box-sizing: border-box;
}

.toggle-visibility {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #666;
}

.api-key-help {
  margin-top: 12px;
  text-align: center;
}

.api-key-help a {
  color: #1D5DF4;
  text-decoration: none;
  font-size: 14px;
}

.setup-later {
  margin-top: 30px;
  text-align: center;
  border-top: 1px solid #f0f0f0;
  padding-top: 30px;
}

.setup-later-btn {
  background: none;
  border: 1px solid #e0e0e0;
  padding: 12px 24px;
  border-radius: 12px;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

/* Quick Tips 样式 */
.quick-tips-title {
  font-size: 24px;
  color: #333;
  margin: 0 0 24px 0;
  font-weight: 500;
}

.quick-tips-list {
  list-style: none;
  padding: 0;
  margin: 0;
  text-align: left;
}

.quick-tips-list li {
  font-size: 16px;
  color: #666;
  margin-bottom: 16px;
  line-height: 1.5;
  position: relative;
  padding-left: 24px;
}

.quick-tips-list li:before {
  content: "•";
  position: absolute;
  left: 0;
  color: #1D5DF4;
  font-size: 20px;
  line-height: 1;
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Logo 动画 */
.login-logo {
  width: 120px;
  height: 120px;
  margin-bottom: 1rem;
}

.login-logo path[d^="M245.625"] {
  transform-box: fill-box;
  transform-origin: center;
  animation: starPulse 2s ease-in-out infinite;
}

.login-logo path[d^="M245.625"]:nth-of-type(1) { animation-delay: 0s; }
.login-logo path[d^="M245.625"]:nth-of-type(2) { animation-delay: -0.6s; }
.login-logo path[d^="M245.625"]:nth-of-type(3) { animation-delay: -1.2s; }

.login-logo rect[x="340.211"] {
  transform-box: fill-box;
  transform-origin: left;
  animation: rectStretch 3s ease-in-out infinite;
}

.login-logo rect[x="340.211"]:nth-of-type(1) { animation-delay: 0s; }
.login-logo rect[x="340.211"]:nth-of-type(2) { animation-delay: -1s; }
.login-logo rect[x="340.211"]:nth-of-type(3) { animation-delay: -2s; }

@keyframes starPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.15); }
}

@keyframes rectStretch {
  0%, 100% { transform: scaleX(1); }
  50% { transform: scaleX(1.1); }
}
</style>