export default defineContentScript({
  matches: ['<all_urls>'],  // 匹配所有 URL
  main() {
    // 等待 DOM 加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', setupMessageListener);
    } else {
      setupMessageListener();
    }

    Logger.debug('form', 'Content script initialized')
  }
})

// 类型定义
interface FormField {
  element: HTMLElement
  type: string
  id: string
  name: string
  placeholder?: string
  label?: string
}

interface Logger {
  _log: (type: 'info' | 'success' | 'error' | 'debug', category: string, message: string, data?: any) => void
  info: (category: string, message: string, data?: any) => void
  success: (category: string, message: string, data?: any) => void
  error: (message: string, error: any) => void
  debug: (category: string, message: string, data?: any) => void
}

interface Debug {
  enabled: boolean
  form: boolean
  fill: boolean
  api: boolean
  message: boolean
  [key: string]: boolean  // 添加索引签名
}

interface StorageKeys {
  LAST_MODE: string
  PROJECTS: string
  SKIP_LOGIN: string
  LAST_LANGUAGE: string
}

interface FormContent {
  to?: string
  recipient?: string
  subject?: string
  body?: string
  content?: string
  title?: string
  description?: string
  [key: string]: string | undefined
}

interface FillFormCallback {
  success?: () => void;
  error?: (error: any) => void;
}

// Constants
const STORAGE_KEYS: StorageKeys = {
  LAST_MODE: 'formify_last_mode',
  PROJECTS: 'formify_projects',
  SKIP_LOGIN: 'formify_skip_login',
  LAST_LANGUAGE: 'formify_last_language'
}

// 屏蔽特定错误消息
const errorMessages: string[] = [
  'Extension context invalidated',
  'Could not establish connection. Receiving end does not exist',
  'The message port closed before a response was received'
]

// Debug configuration
const DEBUG: Debug = {
  enabled: true,     // 开启总开关
  form: true,        // 开启表单检测相关日志
  fill: true,        // 开启表单填充相关日志
  api: true,         // 开启API响应相关日志
  message: true      // 开启消息相关日志
}

// 重写 console.error 来屏蔽特定错误
const originalConsoleError = console.error
console.error = function(...args: any[]) {
  const shouldBlock = errorMessages.some(msg =>
    args.some(arg =>
      arg instanceof Error ? arg.message.includes(msg) : String(arg).includes(msg)
    )
  )

  if (!shouldBlock) {
    originalConsoleError.apply(console, args)
  }
}

// Logger utility
const Logger: Logger = {
  _log: (type, category, message, data = null) => {
    if (!DEBUG.enabled || !DEBUG[category]) return

    const icons = {
      info: 'ℹ️',
      success: '✅',
      error: '❌',
      debug: '🔍'
    }

    const timestamp = new Date().toISOString().split('T')[1].split('.')[0]
    console.log(`[Formify ${timestamp}] ${icons[type]} [${category}] ${message}`, data || '')
  },

  info: (category, message, data = null) => {
    Logger._log('info', category, message, data)
  },

  success: (category, message, data = null) => {
    Logger._log('success', category, message, data)
  },

  error: (message, error) => {
    // 错误日志始终输出
    console.error(`[Formify] ❌ ${message}`, error)
  },

  debug: (category, message, data = null) => {
    Logger._log('debug', category, message, data)
  }
}

// 添加邮件客户端配置
const emailClients = {
  gmail: {
    recipient: 'input.agP.aFw[role="combobox"], div[role="textbox"][aria-label*="To"], div[role="textbox"][aria-label*="收件人"], div[role="textbox"][aria-label*="收件者"], div[role="textbox"][aria-label*="宛先"]',
    subject: 'input.aoT[name="subjectbox"], input[aria-label*="Subject"], input[aria-label*="主题"], input[aria-label*="主旨"], input[aria-label*="件名"]',
    body: 'div.Am.aiL.Al.editable.LW-avf[contenteditable="true"], div[role="textbox"][aria-label="Message Body"], div[role="textbox"][aria-label*="正文"], div[role="textbox"][aria-label*="内容"], div[role="textbox"][aria-label*="本文"]'
  },
  outlook: {
    recipient: 'div[role="textbox"].EditorClass, div[role="textbox"][aria-label*="To"], div[role="textbox"][aria-label*="收件人"], div[role="textbox"][aria-label*="收件者"], div[role="textbox"][aria-label*="宛先"]',
    subject: 'input.fui-Input__input.zrkM_, input[aria-label*="Subject"], input[aria-label*="主题"], input[aria-label*="主旨"], input[aria-label*="件名"]',
    body: 'div[role="textbox"].DziEn, div[contenteditable="true"][aria-label*="Message body"], div[contenteditable="true"][aria-label*="正文"], div[contenteditable="true"][aria-label*="内容"], div[contenteditable="true"][aria-label*="本文"]'
  },
  yahoo: {
    recipient: 'input#message-to-field, input[placeholder*="To"], input[placeholder*="收件人"]',
    subject: 'input#mail-subject, input[placeholder*="Subject"], input[placeholder*="主题"]',
    body: 'div[contenteditable="true"][aria-label="Message body"], div[contenteditable="true"][aria-label*="正文"]'
  },
  protonmail: {
    recipient: 'input[placeholder="Email addresses"], input[placeholder*="To"], input[placeholder*="收件人"]',
    subject: 'input[placeholder="Subject"], input[placeholder*="主题"]',
    body: 'div[contenteditable="true"].ProseMirror, div[contenteditable="true"][aria-label*="body"]'
  }
} as const;

// 更新 detectFormFields 函数
function detectFormFields(): FormField[] {
  const formFields: FormField[] = [];

  Logger.debug('form', 'Starting form field detection');

  // 检测邮件客户端
  Object.entries(emailClients).forEach(([client, selectors]) => {
    Object.entries(selectors).forEach(([fieldType, selector]) => {
      const element = document.querySelector(selector);
      if (element) {
        formFields.push({
          element: element as HTMLElement,
          type: element.tagName.toLowerCase(),
          id: element.id,
          name: fieldType,
          placeholder: (element as HTMLInputElement).placeholder || '',
          label: fieldType.charAt(0).toUpperCase() + fieldType.slice(1)
        });
      }
    });
  });

  // 如果没有找到特定的邮件客户端字段，继续检查其他字段
  if (formFields.length === 0) {
    // 获取所有可能的表单元素
    const allTextboxes = document.querySelectorAll('[role="textbox"]');
    const allInputs = document.querySelectorAll('input:not([type="hidden"]):not([type="submit"]):not([type="button"]):not([type="reset"])');
    const allTextareas = document.querySelectorAll('textarea');
    const allSelects = document.querySelectorAll('select');
    const allContentEditables = document.querySelectorAll('[contenteditable="true"]');
    const allEditorFrames = document.querySelectorAll('iframe.cke_wysiwyg_frame, iframe.tox-edit-area__iframe');

    Logger.debug('form', 'Found base elements count', {
      textboxes: allTextboxes.length,
      inputs: allInputs.length,
      textareas: allTextareas.length,
      selects: allSelects.length,
      contentEditables: allContentEditables.length,
      editorFrames: allEditorFrames.length
    });

    // 检查是否是 GitHub 页面
    if (window.location.hostname.includes('github.com')) {
      Logger.debug('form', 'Detected GitHub page, using generic form detection');

      // 使用通用的 GitHub Issue 表单元素查找函数
      const { titleInput, descriptionInput, taskList } = findGitHubIssueFormElements();

      // 将找到的元素添加到表单字段列表
      if (titleInput) {
        formFields.push({
          element: titleInput,
          type: 'text',
          id: titleInput.id,
          name: 'title',
          placeholder: titleInput.getAttribute('placeholder') || '',
          label: titleInput.getAttribute('aria-label') || 'Title'
        });
      }

      if (descriptionInput) {
        formFields.push({
          element: descriptionInput,
          type: descriptionInput.tagName.toLowerCase() === 'textarea' ? 'textarea' : 'contenteditable',
          id: descriptionInput.id,
          name: 'description',
          placeholder: descriptionInput.getAttribute('placeholder') || '',
          label: 'Description'
        });
      }

      if (taskList) {
        formFields.push({
          element: taskList,
          type: 'tasklist',
          id: taskList.id,
          name: 'tasklist',
          placeholder: '',
          label: 'Task List'
        });
      }
    }

    // 通用字段处理
    const commonInputs = Array.from(allInputs).filter(input => {
      const type = input.getAttribute('type');
      return type === 'text' || type === 'textarea' || type === 'email' ||
             type === 'tel' || type === 'url' || type === 'number' ||
             type === 'date' || type === 'search' || !type;
    });

    // 处理通用输入字段
    commonInputs.forEach(input => {
      const isDuplicate = formFields.some(field => field.element === input);
      if (!isDuplicate && isVisibleElement(input as HTMLElement)) {
        const label = findLabel(input as HTMLElement);
        formFields.push({
          element: input as HTMLElement,
          type: input.getAttribute('type') || 'text',
          id: input.id,
          name: input.getAttribute('name') || input.id || getNameFromAttributes(input),
          placeholder: input.getAttribute('placeholder') || '',
          label
        });
      }
    });

    // 处理 textarea 元素
    Array.from(allTextareas).forEach(textarea => {
      const isDuplicate = formFields.some(field => field.element === textarea);
      if (!isDuplicate && isVisibleElement(textarea as HTMLElement)) {
        const label = findLabel(textarea as HTMLElement);
        formFields.push({
          element: textarea as HTMLElement,
          type: 'textarea',
          id: textarea.id,
          name: textarea.getAttribute('name') || textarea.id || getNameFromAttributes(textarea),
          placeholder: textarea.getAttribute('placeholder') || '',
          label
        });
      }
    });

    // 处理 select 元素
    Array.from(allSelects).forEach(select => {
      const isDuplicate = formFields.some(field => field.element === select);
      if (!isDuplicate && isVisibleElement(select as HTMLElement)) {
        const label = findLabel(select as HTMLElement);
        formFields.push({
          element: select as HTMLElement,
          type: 'select',
          id: select.id,
          name: select.getAttribute('name') || select.id || getNameFromAttributes(select),
          placeholder: '',
          label
        });
      }
    });

    // 处理 contenteditable 元素
    Array.from(allContentEditables).forEach(element => {
      const isDuplicate = formFields.some(field => field.element === element);
      if (!isDuplicate && isVisibleElement(element as HTMLElement)) {
        const label = findLabel(element as HTMLElement);
        formFields.push({
          element: element as HTMLElement,
          type: 'contenteditable',
          id: element.id,
          name: element.getAttribute('name') || element.id || getNameFromAttributes(element),
          placeholder: '',
          label
        });
      }
    });

    // 处理编辑器 iframe
    Array.from(allEditorFrames).forEach(iframe => {
      const isDuplicate = formFields.some(field => field.element === iframe);
      if (!isDuplicate && isVisibleElement(iframe as HTMLElement)) {
        const label = findLabel(iframe as HTMLElement);
        formFields.push({
          element: iframe as HTMLElement,
          type: 'iframe-editor',
          id: iframe.id,
          name: iframe.getAttribute('name') || iframe.id || 'editor',
          placeholder: '',
          label: label || 'Editor'
        });
      }
    });
  }

  Logger.debug('form', 'Detected form fields:', formFields);
  return formFields;
}

// 检查元素是否可见
function isVisibleElement(element: HTMLElement): boolean {
  const style = window.getComputedStyle(element);
  return style.display !== 'none' &&
         style.visibility !== 'hidden' &&
         style.opacity !== '0' &&
         element.offsetWidth > 0 &&
         element.offsetHeight > 0;
}

// 通用的 GitHub Issue 表单元素查找函数
function findGitHubIssueFormElements(): {
  titleInput: HTMLElement | null,
  descriptionInput: HTMLElement | null,
  taskList: HTMLElement | null
} {
  Logger.debug('form', 'Using generic GitHub issue form detection');

  let titleInput: HTMLElement | null = null;
  let descriptionInput: HTMLElement | null = null;
  let taskList: HTMLElement | null = null;

  // 1. 基于页面上下文检测 GitHub issue 页面
  const isIssuePage = window.location.pathname.includes('/issues/') ||
                     window.location.pathname.includes('/issues/new') ||
                     window.location.pathname.includes('/pull/') ||
                     window.location.pathname.includes('/pull/new');

  if (!isIssuePage) {
    Logger.debug('form', 'Not a GitHub issue page');
    return { titleInput, descriptionInput, taskList };
  }

  // 2. 查找表单容器
  const formContainer = document.querySelector('form.js-new-issue-form, .js-issues-listing, .js-new-comment-form');
  Logger.debug('form', 'Form container found:', !!formContainer);

  // 3. 查找标题输入框 - 使用多种策略
  // 策略 1: 基于语义和属性
  const titleCandidates = [
    // 基于语义
    document.querySelector('input[aria-label*="title" i], input[aria-label*="标题" i]'),
    // 基于 ID 和名称
    document.querySelector('input#issue_title, input[name="issue[title]"], input[name*="title" i]'),
    // 基于占位符
    document.querySelector('input[placeholder*="title" i], input[placeholder*="标题" i]')
  ].filter(Boolean);

  // 策略 2: 如果没有找到，查找页面上所有可见的文本输入框
  if (titleCandidates.length === 0) {
    const allTextInputs = document.querySelectorAll('input[type="text"]:not([hidden])');
    for (const input of Array.from(allTextInputs)) {
      if (isVisibleElement(input as HTMLElement)) {
        titleCandidates.push(input);
      }
    }
  }

  // 选择第一个可见的标题输入框
  for (const candidate of titleCandidates) {
    if (candidate && isVisibleElement(candidate as HTMLElement)) {
      titleInput = candidate as HTMLElement;
      break;
    }
  }

  // 4. 查找描述文本区域 - 使用多种策略
  // 策略 1: 基于语义和属性
  const descCandidates = [
    // 基于语义
    document.querySelector('textarea[aria-label*="body" i], textarea[aria-label*="description" i], textarea[aria-label*="markdown" i], textarea[aria-label*="内容" i], textarea[aria-label*="描述" i]'),
    // 基于 ID 和名称
    document.querySelector('textarea#issue_body, textarea[name="issue[body]"], textarea[name*="body" i], textarea[name*="description" i]'),
    // 基于占位符
    document.querySelector('textarea[placeholder*="description" i], textarea[placeholder*="write" i], textarea[placeholder*="描述" i], textarea[placeholder*="内容" i]'),
    // 基于角色
    document.querySelector('[role="textbox"][aria-label*="body" i], [role="textbox"][aria-label*="comment" i], [role="textbox"][data-gramm="false"]'),
    // 基于数据属性
    document.querySelector('textarea[data-resize], [data-testid*="body" i], [data-testid*="description" i]')
  ].filter(Boolean);

  // 策略 2: 如果没有找到，查找页面上所有可见的文本区域
  if (descCandidates.length === 0) {
    const allTextareas = document.querySelectorAll('textarea:not([hidden]), [role="textbox"], [contenteditable="true"]');
    for (const textarea of Array.from(allTextareas)) {
      if (isVisibleElement(textarea as HTMLElement)) {
        descCandidates.push(textarea);
      }
    }
  }

  // 选择第一个可见的描述文本区域
  for (const candidate of descCandidates) {
    if (candidate && isVisibleElement(candidate as HTMLElement)) {
      descriptionInput = candidate as HTMLElement;
      break;
    }
  }

  // 5. 查找任务列表
  taskList = document.querySelector('.task-list-item, .contains-task-list, .task-list') as HTMLElement;

  // 记录找到的元素
  Logger.debug('form', 'Generic GitHub form detection results:', {
    titleInput: titleInput ? {
      tagName: titleInput.tagName,
      id: titleInput.id,
      className: titleInput.className,
      attributes: {
        name: titleInput.getAttribute('name'),
        ariaLabel: titleInput.getAttribute('aria-label'),
        placeholder: titleInput.getAttribute('placeholder')
      }
    } : null,
    descriptionInput: descriptionInput ? {
      tagName: descriptionInput.tagName,
      id: descriptionInput.id,
      className: descriptionInput.className,
      attributes: {
        name: descriptionInput.getAttribute('name'),
        ariaLabel: descriptionInput.getAttribute('aria-label'),
        role: descriptionInput.getAttribute('role'),
        placeholder: descriptionInput.getAttribute('placeholder'),
        dataResize: descriptionInput.getAttribute('data-resize'),
        dataTestId: descriptionInput.getAttribute('data-testid')
      }
    } : null,
    taskList: !!taskList
  });

  return { titleInput, descriptionInput, taskList };
}

// 从其他属性中获取可能的名称
function getNameFromAttributes(element: Element): string {
  // 尝试从各种属性中获取名称
  return element.getAttribute('aria-label') ||
         element.getAttribute('data-name') ||
         element.getAttribute('data-field-name') ||
         element.getAttribute('data-testid') ||
         element.getAttribute('title') ||
         '';
}

// Label finding helper function
function findLabel(input: HTMLElement): string {
  // Try to find label by for attribute
  if (input.id) {
    const label = document.querySelector(`label[for="${input.id}"]`)
    if (label) return label.textContent?.trim() || ''
  }

  // Try to find parent label
  const parentLabel = input.closest('label')
  if (parentLabel) return parentLabel.textContent?.trim() || ''

  // Try to find nearby text that might be a label
  const previousElement = input.previousElementSibling
  if (previousElement && previousElement.tagName !== 'INPUT' && previousElement.tagName !== 'TEXTAREA') {
    return previousElement.textContent?.trim() || ''
  }

  return ''
}

// 添加一个全局变量来跟踪样式标签
let generatingStyleTag: HTMLStyleElement | null = null

// 显示生成中的视觉效果
function showGeneratingEffect() {
  try {
    // 如果已经有样式标签，先移除
    if (generatingStyleTag && generatingStyleTag.parentNode) {
      generatingStyleTag.parentNode.removeChild(generatingStyleTag)
    }

    // 检测表单字段
    const formFields = detectFormFields()
    if (!formFields.length) {
      Logger.debug('effect', 'No form fields detected for showing effect')
      return
    }

    // 添加加载动画样式
    generatingStyleTag = document.createElement('style')
    generatingStyleTag.textContent = `
      @keyframes formifyBorderGlow {
        0% { outline: 2px solid rgba(33, 150, 243, 0.4); box-shadow: 0 0 5px rgba(33, 150, 243, 0.4); }
        50% { outline: 2px solid rgba(33, 150, 243, 0.8); box-shadow: 0 0 15px rgba(33, 150, 243, 0.6); }
        100% { outline: 2px solid rgba(33, 150, 243, 0.4); box-shadow: 0 0 5px rgba(33, 150, 243, 0.4); }
      }
      .formify-loading {
        animation: formifyBorderGlow 1.5s ease-in-out infinite !important;
        z-index: 9999;
        position: relative;
      }
    `
    document.head.appendChild(generatingStyleTag)

    // 给所有表单字段添加加载动画
    formFields.forEach(field => {
      if (field.element) {
        field.element.classList.add('formify-loading')
      }
    })

    Logger.debug('effect', 'Added generating effect to form fields')
  } catch (error) {
    Logger.error('Error showing generating effect:', error)
  }
}

// 移除生成中的视觉效果
function removeGeneratingEffect() {
  try {
    // 移除所有加载动画
    document.querySelectorAll('.formify-loading').forEach(el => {
      el.classList.remove('formify-loading')
    })

    // 移除样式标签
    if (generatingStyleTag && generatingStyleTag.parentNode) {
      generatingStyleTag.parentNode.removeChild(generatingStyleTag)
      generatingStyleTag = null
    }

    Logger.debug('effect', 'Removed generating effect from form fields')
  } catch (error) {
    Logger.error('Error removing generating effect:', error)
  }
}

// 更新 fillForm 函数
function fillForm(formContent: FormContent, callback?: FillFormCallback): void {
  try {
    Logger.debug('fill', 'Starting form fill with content:', formContent)

    // 检查是否需要解析 JSON
    let contentToUse = formContent

    // 记录原始内容结构
    Logger.debug('fill', 'Original form content structure:', {
      type: typeof formContent,
      isObject: typeof formContent === 'object',
      hasContentProperty: typeof formContent === 'object' && 'content' in formContent,
      contentType: typeof formContent === 'object' && formContent.content ? typeof formContent.content : 'N/A',
      keys: typeof formContent === 'object' ? Object.keys(formContent) : [],
      preview: typeof formContent === 'string' ? formContent.substring(0, 100) : 'Not a string'
    });

    if (typeof formContent === 'object' && formContent.content && typeof formContent.content === 'string') {
      try {
        Logger.debug('fill', 'Attempting to parse JSON content:', formContent.content.substring(0, 200) + '...')
        const parsedContent = JSON.parse(formContent.content)
        Logger.debug('fill', 'Successfully parsed JSON content:', {
          type: typeof parsedContent,
          isObject: typeof parsedContent === 'object',
          keys: typeof parsedContent === 'object' ? Object.keys(parsedContent) : [],
          preview: JSON.stringify(parsedContent).substring(0, 200) + '...'
        })
        contentToUse = parsedContent
      } catch (e) {
        Logger.error('Failed to parse JSON content:', e)
        Logger.debug('fill', 'Using original content due to JSON parse failure')
        // 如果解析失败，继续使用原始内容
      }
    }

    Logger.debug('fill', 'Form content structure:', {
      hasContent: !!contentToUse,
      contentType: typeof contentToUse,
      keys: contentToUse ? Object.keys(contentToUse) : [],
      values: contentToUse ? Object.entries(contentToUse).map(([key, value]) => ({
        key,
        type: typeof value,
        hasValue: !!value,
        valuePreview: typeof value === 'string' ? value.substring(0, 50) : value,
        fullValue: value // 添加完整值的输出
      })) : []
    })

    // 移除生成中的视觉效果
    removeGeneratingEffect()

    // 检测表单字段
    const formFields = detectFormFields()
    if (!formFields.length) {
      throw new Error('No form fields detected')
    }

    // 先处理主题字段
    const subjectField = formFields.find(field => field.name === 'subject')
    if (subjectField && subjectField.element && contentToUse.subject) {
      Logger.debug('fill', 'Processing subject field...', { value: contentToUse.subject })
      try {
        // 检查是否是 React 组件
        const isReactComponent = subjectField.element.classList.contains('fui-Input__input') ||
                               subjectField.element.hasAttribute('data-reactid') ||
                               !!subjectField.element.closest('[data-reactroot]')

        if (isReactComponent) {
          // 使用 React 组件的处理方式
          if (subjectField.element instanceof HTMLInputElement) {
            const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
              window.HTMLInputElement.prototype,
              "value"
            )?.set;

            if (nativeInputValueSetter) {
              // 调用setter设置值（忽略返回值）
              void nativeInputValueSetter.call(subjectField.element, contentToUse.subject);

              // 触发一系列事件来确保 React 状态更新
              ['input', 'change', 'blur'].forEach(eventType => {
                const event = new Event(eventType, {
                  bubbles: true,
                  composed: true,
                  cancelable: true
                })
                subjectField.element.dispatchEvent(event)
              });

              // 确保元素获得焦点
              subjectField.element.focus()

              // 创建并触发输入事件
              const inputEvent = new InputEvent('input', {
                bubbles: true,
                cancelable: true,
                inputType: 'insertText',
                data: contentToUse.subject
              })
              subjectField.element.dispatchEvent(inputEvent)

              // 最后失去焦点
              subjectField.element.blur()

              Logger.debug('fill', 'Filled React input component')
            }
          }
        } else {
          // 如果不是 React 组件，使用普通方式
          fillFieldContent(subjectField, contentToUse.subject)
        }
      } catch (e) {
        Logger.error('Error filling subject field:', e)
      }
    }

    // 处理其他字段
    formFields.forEach(field => {
      // 跳过主题字段，因为已经处理过了
      if (field.name === 'subject') return

      try {
        let content: string | undefined

        if (field.name === 'to' || field.name === 'recipient') {
          content = contentToUse.to || contentToUse.recipient || contentToUse.email || contentToUse.emailAddress || contentToUse.email_address
          Logger.debug('fill', `Matching recipient field: ${field.name}`, { content })

          // 检查是否是 Gmail 或 Outlook 的收件人字段
          const isGmailRecipient = field.element.classList.contains('agP') ||
                                field.element.classList.contains('aFw') ||
                                (field.element.getAttribute('role') === 'textbox' &&
                                 (field.element.getAttribute('aria-label')?.includes('To') ||
                                  field.element.getAttribute('aria-label')?.includes('收件人')));

          const isOutlookRecipient = field.element.classList.contains('EditorClass') ||
                                  (field.element.getAttribute('role') === 'textbox' &&
                                   (field.element.getAttribute('aria-label')?.includes('To') ||
                                    field.element.getAttribute('aria-label')?.includes('收件人')));

          // 记录特殊字段类型以便后续处理
          if (isGmailRecipient) {
            field.type = 'gmail-recipient';
          } else if (isOutlookRecipient) {
            field.type = 'outlook-recipient';
          }
        } else if (field.name === 'body' || field.name === 'content' || field.name === 'editor') {
          content = contentToUse[''] || contentToUse.body || contentToUse.content || contentToUse.message
          Logger.debug('fill', `Matching body field: ${field.name}`, { content })
        } else if (field.name === 'title' || field.name === 'description') {
          // 增强 GitHub 字段匹配策略
          const possibleTitleKeys = ['title', ':r1:', ':r21:', 'issue_title', 'issue[title]', 'name', 'summary', 'subject'];
          const possibleDescKeys = ['description', ':r6:', ':r26:', 'issue_body', 'issue[body]', 'body', 'content', 'details', 'comment', 'message'];

          if (field.name === 'title') {
            // 尝试所有可能的标题键
            for (const key of possibleTitleKeys) {
              if (contentToUse[key]) {
                content = contentToUse[key];
                Logger.debug('fill', `Found title content using key: ${key}`);
                break;
              }
            }

            // 如果没有找到内容，尝试直接使用第一个键值对
            if (!content && Object.keys(contentToUse).length > 0) {
              const firstKey = Object.keys(contentToUse)[0];
              if (typeof contentToUse[firstKey] === 'string' &&
                  contentToUse[firstKey].length < 100) { // 标题通常较短
                content = contentToUse[firstKey];
                Logger.debug('fill', `Using first content key as title: ${firstKey}`);
              }
            }
          } else if (field.name === 'description') {
            // 尝试所有可能的描述键
            for (const key of possibleDescKeys) {
              if (contentToUse[key]) {
                content = contentToUse[key];
                Logger.debug('fill', `Found description content using key: ${key}`);
                break;
              }
            }

            // 如果没有找到内容，尝试使用最长的字符串值
            if (!content) {
              let longestContent = '';
              for (const [key, value] of Object.entries(contentToUse)) {
                if (typeof value === 'string' && value.length > longestContent.length && value.length > 100) {
                  longestContent = value;
                  Logger.debug('fill', `Found longer content for description using key: ${key}`);
                }
              }
              if (longestContent) {
                content = longestContent;
              }
            }

            // 如果仍然没有找到内容，尝试使用整个内容对象
            if (!content && typeof contentToUse === 'object') {
              try {
                // 将整个内容对象转换为格式化的文本
                const formattedContent = Object.entries(contentToUse)
                  .filter(([key, value]) => typeof value === 'string' && value.trim() !== '')
                  .map(([key, value]) => `${key}: ${value}`)
                  .join('\n\n');

                if (formattedContent) {
                  content = formattedContent;
                  Logger.debug('fill', 'Using formatted content object as description');
                }
              } catch (e) {
                Logger.error('Error formatting content object:', e);
              }
            }
          }

          Logger.debug('fill', `Matching GitHub field: ${field.name}`, {
            fieldFound: !!field.element,
            contentFound: !!content,
            elementType: field.element?.tagName,
            elementClasses: field.element?.className,
            elementId: field.element?.id,
            elementName: field.element?.getAttribute('name'),
            elementRole: field.element?.getAttribute('role'),
            contentPreview: content?.substring(0, 50),
            contentKeys: Object.keys(contentToUse),
            fieldType: field.name,
            selectedContent: content
          });
        } else {
          // 更通用的内容匹配逻辑
          const labelNoAsterisk = field.label?.replace(/\s*\*\s*$/, '') || ''
          const labelFirstWord = field.label?.split(/[\s*]/)[0] || ''

          // 创建一个根据字段语言特性进行匹配的函数
          const findValueByLanguage = (obj: any, key: string): string | undefined => {
            if (!key) return undefined

            // 直接匹配
            if (obj[key] !== undefined) return obj[key]

            // 不区分大小写匹配
            const lowerKey = key.toLowerCase()
            const keyMatch = Object.keys(obj).find(k => k.toLowerCase() === lowerKey)
            if (keyMatch) return obj[keyMatch]

            // 部分匹配
            const partialMatch = Object.keys(obj).find(k =>
              k.toLowerCase().includes(lowerKey) || lowerKey.includes(k.toLowerCase())
            )
            if (partialMatch) return obj[partialMatch]

            // 尝试智能匹配常见字段
            const commonFieldMappings: Record<string, string[]> = {
              'name': ['name', 'fullname', 'full_name', 'full-name', 'username', 'user', 'firstname', 'first_name', 'first-name'],
              'email': ['email', 'email_address', 'email-address', 'mail'],
              'phone': ['phone', 'telephone', 'tel', 'mobile', 'cell', 'phone_number', 'phone-number'],
              'address': ['address', 'street', 'location'],
              'city': ['city', 'town'],
              'state': ['state', 'province', 'region'],
              'country': ['country', 'nation'],
              'zip': ['zip', 'zipcode', 'postal', 'postal_code', 'postal-code'],
              'company': ['company', 'organization', 'organisation', 'employer'],
              'website': ['website', 'site', 'url', 'web', 'homepage'],
              'message': ['message', 'comment', 'feedback', 'note', 'description', 'details']
            }

            for (const [category, synonyms] of Object.entries(commonFieldMappings)) {
              if (synonyms.some(s => lowerKey.includes(s) || s.includes(lowerKey))) {
                const categoryMatch = Object.keys(obj).find(k =>
                  commonFieldMappings[category]?.some(s => k.toLowerCase().includes(s))
                )
                if (categoryMatch) return obj[categoryMatch]
              }
            }

            return undefined
          }

          // 尝试多种匹配方式
          content = findValueByLanguage(contentToUse, field.name) ||
                   findValueByLanguage(contentToUse, field.id) ||
                   findValueByLanguage(contentToUse, labelNoAsterisk) ||
                   findValueByLanguage(contentToUse, labelFirstWord) ||
                   findValueByLanguage(contentToUse, field.placeholder || '')

          Logger.debug('fill', `Matching field: ${field.name}`, {
            label: field.label,
            content,
            matchAttempts: [field.name, field.id, labelNoAsterisk, labelFirstWord, field.placeholder]
          })
        }

        if (content) {
          fillFieldContent(field, content)
        } else {
          // 如果没有找到匹配的内容，只移除加载动画
        }
      } catch (e) {
        Logger.error(`Error filling field ${field.name}:`, e)
      }
    })

    // 回调处理
    if (callback) {
      callback.success?.()
    }
  } catch (error) {
    Logger.error('Error filling form:', error)

    // 回调处理
    if (callback) {
      callback.error?.(error)
    }
  }
}

// 更新 fillFieldContent 函数
function fillFieldContent(field: FormField, content: string): void {
  if (!field.element || !content) {
    Logger.debug('fill', `Skipping field fill due to missing element or content`, {
      hasElement: !!field.element,
      hasContent: !!content,
      fieldName: field.name
    });
    return;
  }

  // 特殊处理 GitHub 字段
  if (window.location.hostname.includes('github.com')) {
    Logger.debug('fill', `Processing GitHub field: ${field.name}`, {
      elementType: field.element.tagName,
      elementId: field.element.id,
      elementClasses: field.element.className,
      ariaLabel: field.element.getAttribute('aria-label'),
      dataResize: field.element.getAttribute('data-resize'),
      placeholder: field.element.getAttribute('placeholder')
    });

    try {
      // 通用处理 GitHub 元素的方法
      if (field.name === 'description' || field.name === 'body') {
        // 处理描述字段
        if (field.element.tagName.toLowerCase() === 'textarea') {
          Logger.debug('fill', 'Filling GitHub textarea element');

          // 尝试使用 React 特殊处理方式
          const nativeValueSetter = Object.getOwnPropertyDescriptor(
            window.HTMLTextAreaElement.prototype,
            "value"
          )?.set;

          if (nativeValueSetter) {
            // 聚焦元素
            field.element.focus();

            // 使用原生 setter 设置值
            nativeValueSetter.call(field.element, content);

            // 触发一系列事件来确保 React 状态更新
            ['input', 'change', 'blur'].forEach(eventType => {
              const event = new Event(eventType, {
                bubbles: true,
                composed: true,
                cancelable: true
              });
              field.element.dispatchEvent(event);
            });

            // 创建并触发输入事件
            const inputEvent = new InputEvent('input', {
              bubbles: true,
              cancelable: true,
              inputType: 'insertText',
              data: content
            });
            field.element.dispatchEvent(inputEvent);

            // 最后失去焦点
            field.element.blur();

            Logger.debug('fill', 'Filled GitHub textarea using React method');
            return;
          }
        } else if (field.element.getAttribute('role') === 'textbox' || field.element.getAttribute('contenteditable') === 'true') {
          // 处理 role="textbox" 或 contenteditable 元素
          Logger.debug('fill', 'Filling GitHub textbox/contenteditable element');

          // 聚焦元素
          field.element.focus();

          // 清空现有内容
          field.element.innerHTML = '';

          // 处理换行符，将\n转换为<br>标签
          const formattedContent = content.replace(/\n/g, '<br>');
          field.element.innerHTML = formattedContent;

          // 触发必要的事件
          ['input', 'change', 'blur'].forEach(eventType => {
            const event = new Event(eventType, { bubbles: true, composed: true });
            field.element.dispatchEvent(event);
          });

          Logger.debug('fill', 'Filled GitHub textbox/contenteditable element');
          return;
        }
      } else if (field.name === 'title') {
        // 处理标题字段
        if (field.element.tagName.toLowerCase() === 'input') {
          Logger.debug('fill', 'Filling GitHub title input element');

          // 尝试使用 React 特殊处理方式
          const nativeValueSetter = Object.getOwnPropertyDescriptor(
            window.HTMLInputElement.prototype,
            "value"
          )?.set;

          if (nativeValueSetter) {
            // 聚焦元素
            field.element.focus();

            // 使用原生 setter 设置值
            nativeValueSetter.call(field.element, content);

            // 触发一系列事件来确保 React 状态更新
            ['input', 'change', 'blur'].forEach(eventType => {
              const event = new Event(eventType, {
                bubbles: true,
                composed: true,
                cancelable: true
              });
              field.element.dispatchEvent(event);
            });

            // 创建并触发输入事件
            const inputEvent = new InputEvent('input', {
              bubbles: true,
              cancelable: true,
              inputType: 'insertText',
              data: content
            });
            field.element.dispatchEvent(inputEvent);

            // 最后失去焦点
            field.element.blur();

            Logger.debug('fill', 'Filled GitHub title input using React method');
            return;
          }
        }
      }
    } catch (e) {
      Logger.error('Error filling GitHub element:', e);
    }
  }

  // 特殊处理 Gmail 和 Outlook 的收件人字段
  if (field.type === 'gmail-recipient' || field.type === 'outlook-recipient') {
    try {
      // 聚焦元素
      field.element.focus();

      // 清空现有内容
      if (field.element.tagName === 'INPUT') {
        (field.element as HTMLInputElement).value = content;
      } else if (field.element.getAttribute('contenteditable') === 'true') {
        field.element.innerHTML = '';
        const textNode = document.createTextNode(content);
        field.element.appendChild(textNode);
      } else {
        // 尝试设置值
        (field.element as any).value = content;
      }

      // 触发必要的事件
      ['input', 'change'].forEach(eventType => {
        const event = new Event(eventType, { bubbles: true, composed: true });
        field.element.dispatchEvent(event);
      });

      // 模拟按下回车键来确认收件人
      setTimeout(() => {
        const enterEvent = new KeyboardEvent('keydown', {
          key: 'Enter',
          code: 'Enter',
          keyCode: 13,
          which: 13,
          bubbles: true,
          cancelable: true
        });
        field.element.dispatchEvent(enterEvent);

        // 失去焦点
        setTimeout(() => {
          field.element.blur();
        }, 100);
      }, 100);

      Logger.debug('fill', `Filled ${field.type} field`);
      return;
    } catch (e) {
      Logger.error(`Error filling ${field.type} field:`, e);
    }
  }

  try {
    Logger.debug('fill', `Preparing to fill field: ${field.name}`, {
      content: content.substring(0, 50) + (content.length > 50 ? '...' : ''),
      fullContent: content, // 添加完整内容输出
      elementDetails: {
        tagName: field.element.tagName,
        type: field.element instanceof HTMLInputElement ? field.element.type : 'not-input',
        classes: field.element.className,
        id: field.element.id,
        name: field.name,
        isVisible: isVisibleElement(field.element),
        attributes: Array.from(field.element.attributes).map(attr => ({
          name: attr.name,
          value: attr.value
        }))
      }
    });

    // 在填充之前记录字段的当前值
    const previousValue = field.element instanceof HTMLInputElement || field.element instanceof HTMLTextAreaElement
      ? field.element.value
      : field.element.textContent;

    Logger.debug('fill', `Current field state before filling:`, {
      fieldName: field.name,
      previousValue
    });

    // 检查是否是 React 组件
    const isReactComponent = field.element.classList.contains('fui-Input__input') ||
                             field.element.hasAttribute('data-reactid') ||
                             !!field.element.closest('[data-reactroot]');

    // 检查是否是富文本编辑器
    const isRichTextEditor = field.element.getAttribute('contenteditable') === 'true' ||
                             field.element.classList.contains('ck-editor__editable') ||
                             field.element.classList.contains('tox-edit-area__iframe') ||
                             field.element.classList.contains('mce-content-body');

    // 检查是否是 iframe 编辑器
    const isIframeEditor = field.element.tagName === 'IFRAME' &&
                          (field.element.classList.contains('cke_wysiwyg_frame') ||
                           field.element.classList.contains('tox-edit-area__iframe'));

    // 处理 iframe 编辑器
    if (isIframeEditor) {
      try {
        const iframe = field.element as HTMLIFrameElement;
        const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document;

        if (iframeDocument && iframeDocument.body) {
          iframeDocument.body.innerHTML = content;

          // 触发 iframe 内容变化事件
          const event = new Event('input', { bubbles: true });
          iframeDocument.body.dispatchEvent(event);

          Logger.debug('fill', 'Filled iframe editor content');
        }
      } catch (e) {
        Logger.error('Error filling iframe editor:', e);
      }
      return;
    }

    // 处理富文本编辑器
    if (isRichTextEditor) {
      // 检查是否是 Gmail 的内容编辑器
      const isGmailEditor = field.element.classList.contains('editable') ||
                          field.element.classList.contains('LW-avf') ||
                          field.element.closest('.Am.Al.editable') !== null ||
                          (field.element.getAttribute('role') === 'textbox' &&
                           field.element.closest('[aria-label="Message Body"]') !== null);

      // 检查是否是 Outlook 的内容编辑器
      const isOutlookEditor = field.element.getAttribute('role') === 'textbox' &&
                            (field.element.classList.contains('DziEn') ||
                             field.element.hasAttribute('aria-label') &&
                             (field.element.getAttribute('aria-label')?.includes('Message body') ||
                              field.element.getAttribute('aria-label')?.includes('正文')));

      if (isGmailEditor) {
        try {
          // 聚焦元素
          field.element.focus();

          // 清空现有内容
          field.element.innerHTML = '';

          // 处理换行符，将\n转换为<br>标签
          if (field.name === 'body' || field.name === 'content' || field.name === 'message') {
            // 将换行符转换为HTML标签
            const formattedContent = content.replace(/\n/g, '<br>');
            field.element.innerHTML = formattedContent;
          } else {
            // 其他字段使用原始文本
            const textNode = document.createTextNode(content);
            field.element.appendChild(textNode);
          }

          // 触发必要的事件
          ['input', 'change', 'blur'].forEach(eventType => {
            const event = new Event(eventType, { bubbles: true, composed: true });
            field.element.dispatchEvent(event);
          });

          Logger.debug('fill', 'Filled Gmail contenteditable element');
          return;
        } catch (e) {
          Logger.error('Failed to set Gmail contenteditable element value:', e);
        }
      }

      if (isOutlookEditor) {
        try {
          // 聚焦元素
          field.element.focus();

          // 清空现有内容
          field.element.innerHTML = '';

          // 处理换行符，将\n转换为<br>标签
          if (field.name === 'body' || field.name === 'content' || field.name === 'message') {
            // 将换行符转换为HTML标签
            const formattedContent = content.replace(/\n/g, '<br>');
            field.element.innerHTML = formattedContent;
          } else {
            // 其他字段使用原始文本
            const textNode = document.createTextNode(content);
            field.element.appendChild(textNode);
          }

          // 触发必要的事件
          ['input', 'change', 'blur'].forEach(eventType => {
            const event = new Event(eventType, { bubbles: true, composed: true });
            field.element.dispatchEvent(event);
          });

          // 如果是收件人字段，额外处理
          if (field.name === 'to' || field.name === 'recipient') {
            // 模拟按下回车键
            const enterEvent = new KeyboardEvent('keydown', {
              key: 'Enter',
              code: 'Enter',
              keyCode: 13,
              which: 13,
              bubbles: true,
              cancelable: true
            });
            field.element.dispatchEvent(enterEvent);
          }

          Logger.debug('fill', 'Filled Outlook contenteditable element');
          return;
        } catch (e) {
          Logger.error('Failed to set Outlook contenteditable element value:', e);
        }
      }

      // 如果不是特定的邮件客户端，使用通用方法
      // 处理换行符，将\n转换为<br>标签
      if (field.name === 'body' || field.name === 'content' || field.name === 'message') {
        // 将换行符转换为HTML标签
        const formattedContent = content.replace(/\n/g, '<br>');
        field.element.innerHTML = formattedContent;
      } else {
        field.element.innerHTML = content;
      }

      // 触发内容变化事件
      ['input', 'change'].forEach(eventType => {
        const event = new Event(eventType, { bubbles: true });
        field.element.dispatchEvent(event);
      });

      Logger.debug('fill', 'Filled rich text editor content');
      return;
    }

    // 处理 React 组件
    if (isReactComponent) {
      if (field.element instanceof HTMLInputElement || field.element instanceof HTMLTextAreaElement) {
        const nativeValueSetter = Object.getOwnPropertyDescriptor(
          field.element instanceof HTMLInputElement ? window.HTMLInputElement.prototype : window.HTMLTextAreaElement.prototype,
          "value"
        )?.set;

        if (nativeValueSetter) {
          nativeValueSetter.call(field.element, content);

          // 触发一系列事件来确保 React 状态更新
          ['input', 'change', 'blur'].forEach(eventType => {
            const event = new Event(eventType, {
              bubbles: true,
              composed: true,
              cancelable: true
            });
            field.element.dispatchEvent(event);
          });

          // 确保元素获得焦点
          field.element.focus();

          // 创建并触发输入事件
          const inputEvent = new InputEvent('input', {
            bubbles: true,
            cancelable: true,
            inputType: 'insertText',
            data: content
          });
          field.element.dispatchEvent(inputEvent);

          // 最后失去焦点
          field.element.blur();

          Logger.debug('fill', 'Filled React input component');
        }
      } else if (field.element instanceof HTMLSelectElement) {
        // 处理 React select 组件
        fillSelectElement(field.element, content);
      }
      return;
    }

    // 处理普通 HTML 元素
    if (field.element instanceof HTMLInputElement) {
      const input = field.element;

      // 根据输入类型进行处理
      switch (input.type.toLowerCase()) {
        case 'checkbox':
          // 根据内容决定是否选中
          const shouldCheck = /^(yes|true|1|on|checked|selected|enable|enabled)$/i.test(content.trim());
          input.checked = shouldCheck;
          break;

        case 'radio':
          // 只有当内容与值匹配时才选中
          if (input.value.toLowerCase() === content.toLowerCase()) {
            input.checked = true;
          } else {
            // 尝试查找同名的其他单选按钮
            const radioGroup = document.querySelectorAll(`input[type="radio"][name="${input.name}"]`);
            for (const radio of Array.from(radioGroup)) {
              if ((radio as HTMLInputElement).value.toLowerCase() === content.toLowerCase()) {
                (radio as HTMLInputElement).checked = true;
                break;
              }
            }
          }
          break;

        case 'date':
          // 尝试解析日期
          try {
            const date = new Date(content);
            if (!isNaN(date.getTime())) {
              // 格式化为 YYYY-MM-DD
              const year = date.getFullYear();
              const month = String(date.getMonth() + 1).padStart(2, '0');
              const day = String(date.getDate()).padStart(2, '0');
              input.value = `${year}-${month}-${day}`;
            }
          } catch (e) {
            // 如果无法解析，使用原始内容
            input.value = content;
          }
          break;

        default:
          // 对于其他类型的输入框，直接设置值
          input.value = content;
      }

      // 触发事件
      ['input', 'change'].forEach(eventType => {
        const event = new Event(eventType, { bubbles: true });
        input.dispatchEvent(event);
      });

    } else if (field.element instanceof HTMLTextAreaElement) {
      const textarea = field.element;
      textarea.value = content;

      // 触发事件
      ['input', 'change'].forEach(eventType => {
        const event = new Event(eventType, { bubbles: true });
        textarea.dispatchEvent(event);
      });

    } else if (field.element instanceof HTMLSelectElement) {
      fillSelectElement(field.element, content);
    } else {
      // 对于其他类型的元素，尝试设置 textContent
      field.element.textContent = content;

      // 触发通用事件
      ['input', 'change'].forEach(eventType => {
        const event = new Event(eventType, { bubbles: true });
        field.element.dispatchEvent(event);
      });
    }

    // 在填充后记录字段的新值
    const newValue = field.element instanceof HTMLInputElement || field.element instanceof HTMLTextAreaElement
      ? field.element.value
      : field.element.textContent;

    Logger.debug('fill', `Field state after filling:`, {
      fieldName: field.name,
      newValue,
      valueChanged: newValue !== previousValue
    });

    Logger.debug('fill', `Successfully filled field: ${field.name}`);
  } catch (error) {
    Logger.error(`Error filling field ${field.name}:`, error);
  }
}

// 辅助函数：填充 select 元素
function fillSelectElement(select: HTMLSelectElement, content: string): void {
  // 尝试多种方式匹配选项
  const contentLower = content.toLowerCase().trim();
  const options = Array.from(select.options);

  // 1. 尝试完全匹配值
  let matchedOption = options.find(option => option.value === content);

  // 2. 尝试完全匹配文本
  if (!matchedOption) {
    matchedOption = options.find(option => option.text === content);
  }

  // 3. 尝试不区分大小写匹配
  if (!matchedOption) {
    matchedOption = options.find(option =>
      option.value.toLowerCase() === contentLower ||
      option.text.toLowerCase() === contentLower
    );
  }

  // 4. 尝试部分匹配
  if (!matchedOption) {
    matchedOption = options.find(option =>
      option.value.toLowerCase().includes(contentLower) ||
      contentLower.includes(option.value.toLowerCase()) ||
      option.text.toLowerCase().includes(contentLower) ||
      contentLower.includes(option.text.toLowerCase())
    );
  }

  // 如果找到匹配项，设置选中
  if (matchedOption) {
    select.value = matchedOption.value;

    // 触发 change 事件
    const event = new Event('change', { bubbles: true });
    select.dispatchEvent(event);

    Logger.debug('fill', `Selected option: ${matchedOption.text} (${matchedOption.value})`);
  } else {
    Logger.debug('fill', `No matching option found for: ${content}`);
  }
}

// 处理未捕获的 Promise 错误
window.addEventListener('unhandledrejection', function(event) {
  if (errorMessages.some(msg => event.reason?.message?.includes(msg))) {
    event.preventDefault()
    event.stopPropagation()
  }
})

// 处理常规错误
window.addEventListener('error', function(event) {
  if (errorMessages.some(msg => event.error?.message?.includes(msg))) {
    event.preventDefault()
    event.stopPropagation()
  }
}, true)

// 更新 checkPageStatus 函数
function checkPageStatus(): {
  isValid: boolean;
  needsRefresh: boolean;
  hasFormFields: boolean;
  pageType?: string;
} {
  try {
    const isLoaded = document.readyState === 'complete';

    // 检查特定网站
    const isGmail = window.location.hostname.includes('mail.google.com');
    const isOutlook = window.location.hostname.includes('outlook.live.com') ||
                     window.location.hostname.includes('outlook.office.com');
    const isGitHub = window.location.hostname.includes('github.com');

    let pageType = 'unknown';
    if (isGmail) pageType = 'gmail';
    else if (isOutlook) pageType = 'outlook';
    else if (isGitHub) pageType = 'github';

    // 特定网站的加载检查
    let isLoading = false;

    if (isGmail) {
      // Gmail 特定检查
      const mainContent = document.querySelector('div[role="main"]');
      const composeButton = document.querySelector('[gh="cm"]'); // 撰写邮件按钮
      isLoading = !mainContent || !composeButton;
    } else if (isOutlook) {
      // Outlook 特定检查
      const mainContent = document.querySelector('div[role="main"]');
      const newMailButton = document.querySelector('[aria-label*="New mail"], [aria-label*="新邮件"]');
      isLoading = !mainContent || !newMailButton;
    } else if (isGitHub) {
      // GitHub 特定检查 - 增强检测逻辑
      isLoading = !!document.querySelector('.is-loading, .loading');

      // 检查是否在 issue 页面
      const isIssuePage = window.location.pathname.includes('/issues/') ||
                         window.location.pathname.includes('/issues/new');

      // 检查是否有表单元素
      const hasIssueForm = !!document.querySelector('form.js-new-issue-form, .js-issues-listing');

      // 检查是否有标题和描述字段
      const hasTitleField = !!document.querySelector('input[aria-label="Add a title"], input[aria-label="Title"], input[name="issue[title]"], input#issue_title');
      const hasDescField = !!document.querySelector('textarea[name="issue[body]"], textarea#issue_body, [data-testid="issue-body"], [role="textbox"][aria-label="Comment body"]');

      Logger.debug('form', 'GitHub page status check:', {
        isIssuePage,
        hasIssueForm,
        hasTitleField,
        hasDescField,
        isLoading
      });

      // 如果在 issue 页面但没有找到表单元素，可能是页面还在加载
      if (isIssuePage && !hasIssueForm && !hasTitleField && !hasDescField) {
        isLoading = true;
      }
    } else {
      // 通用检查
      isLoading = document.documentElement.classList.contains('loading') ||
                  document.body.classList.contains('loading');
    }

    // 检测表单字段
    const formFields = detectFormFields();
    const hasFields = formFields.length > 0;

    // 如果页面已加载完成且找到了表单字段，就不需要刷新
    const needsRefresh = !isLoaded || (isLoading && !hasFields);

    return {
      isValid: true,
      needsRefresh,
      hasFormFields: hasFields,
      pageType
    };
  } catch (error) {
    Logger.error('Error checking page status:', error);
    return {
      isValid: false,
      needsRefresh: true,
      hasFormFields: false
    };
  }
}

// 修改 setupMessageListener 函数
function setupMessageListener() {
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    try {
      Logger.debug('message', 'Received message:', message);

      switch (message.action || message.type) {
        case 'fillForm':
          Logger.debug('fill', 'Received fillForm message with data:', message.data);
          // 确保我们传递正确的数据结构
          const formContentToFill = message.data.data || message.data;
          fillForm(formContentToFill, {
            success: () => {
              Logger.success('fill', 'Form filled successfully');
              sendResponse({ success: true });
            },
            error: (error) => {
              Logger.error('Failed to fill form:', error);
              sendResponse({ success: false, error: error instanceof Error ? error.message : String(error) });
            }
          });
          return true;

        case 'showGeneratingEffect':
          Logger.debug('effect', 'Received showGeneratingEffect message');
          showGeneratingEffect();
          sendResponse({ success: true });
          return true;

        case 'checkFormFields':
          const formFields = detectFormFields();
          Logger.debug('form', 'Checking form fields, found:', formFields.length);
          sendResponse({
            hasFields: formFields.length > 0,
            fieldCount: formFields.length
          });
          return true;

        case 'checkPageStatus':
          const status = checkPageStatus();
          Logger.debug('form', 'Checking page status:', status);
          sendResponse(status);
          return true;

        case 'getFormFields':
          const fields = detectFormFields();
          Logger.debug('form', 'Getting form fields, found:', fields.length);
          sendResponse({
            success: true,
            fields: fields.map(field => ({
              type: field.type,
              id: field.id,
              name: field.name,
              placeholder: field.placeholder,
              label: field.label
            }))
          });
          return true;

        default:
          Logger.error('Unknown message type:', message);
          sendResponse({ success: false, error: 'Unknown message type' });
          return true;
      }
    } catch (error) {
      Logger.error('Error handling message:', error);
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred'
      });
      return true;
    }
  });

  // 发送初始化成功消息
  chrome.runtime.sendMessage({
    type: 'contentScriptInitialized',
    url: window.location.href
  });

  // 设置 MutationObserver 来监听 DOM 变化
  setupDOMObserver();
}

// 设置 DOM 变化观察器
function setupDOMObserver() {
  // 只在 GitHub 页面上设置观察器
  if (!window.location.hostname.includes('github.com')) {
    return;
  }

  Logger.debug('form', 'Setting up DOM mutation observer for GitHub');

  // 创建一个观察器实例
  const observer = new MutationObserver((mutations) => {
    // 检查是否有相关变化
    const relevantChanges = mutations.some(mutation => {
      // 检查是否添加了新节点
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        // 检查添加的节点是否包含表单元素
        return Array.from(mutation.addedNodes).some(node => {
          if (node.nodeType !== Node.ELEMENT_NODE) return false;

          const element = node as Element;

          // 检查是否是表单元素或包含表单元素
          return element.tagName === 'FORM' ||
                 element.tagName === 'INPUT' ||
                 element.tagName === 'TEXTAREA' ||
                 element.querySelector('input, textarea, [role="textbox"]') !== null;
        });
      }

      // 检查属性变化
      if (mutation.type === 'attributes') {
        const element = mutation.target as Element;

        // 检查是否是表单元素的相关属性
        return element.tagName === 'INPUT' ||
               element.tagName === 'TEXTAREA' ||
               element.getAttribute('role') === 'textbox' ||
               mutation.attributeName === 'contenteditable';
      }

      return false;
    });

    // 如果有相关变化，重新检测表单字段
    if (relevantChanges) {
      Logger.debug('form', 'Detected relevant DOM changes, re-detecting form fields');

      // 使用 setTimeout 来确保 DOM 已经完全更新
      setTimeout(() => {
        const formFields = detectFormFields();
        Logger.debug('form', 'Re-detected form fields after DOM changes:', formFields.length);
      }, 500);
    }
  });

  // 配置观察选项
  const config = {
    childList: true,     // 观察子节点的添加或删除
    subtree: true,       // 观察所有后代节点
    attributes: true,    // 观察属性变化
    attributeFilter: ['role', 'contenteditable', 'class', 'id', 'aria-label'] // 只观察这些属性的变化
  };

  // 开始观察整个文档
  observer.observe(document.documentElement, config);

  Logger.debug('form', 'DOM mutation observer setup complete');
}
