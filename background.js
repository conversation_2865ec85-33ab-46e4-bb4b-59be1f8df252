// Background script for handling extension functionality

// Helper function for logging
const Logger = {
  info: (...args) => console.log('[Formify]', ...args),
  error: (...args) => console.error('[Formify]', ...args),
  success: (...args) => console.log('[Formify Success]', ...args)
};

// API Base URL and Cookie Domain
const API_BASE_URL = 'https://fillify-343190162770.asia-east1.run.app/api';
const COOKIE_DOMAIN = 'fillify.tech';
const COOKIE_URL = 'https://fillify.tech';

// 全局登录状态管理
let isLoggedIn = false;

// 用于追踪最后一次用户信息更新时间
let lastUserInfoFetch = 0;
const USER_INFO_MAX_AGE = 5 * 60 * 1000; // 用户信息最大缓存时间（5分钟）

// 获取用户信息
async function fetchUserInfo(userId, forceUpdate = false) {
  const now = Date.now();
  // 如果不是强制更新，且缓存未过期，直接返回
  if (!forceUpdate && now - lastUserInfoFetch < USER_INFO_MAX_AGE) {
    const { user_info } = await chrome.storage.local.get('user_info');
    if (user_info) {
      return user_info;
    }
  }
  
  lastUserInfoFetch = now;
  try {
    const response = await fetch(`${API_BASE_URL}/users/get-user`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId })
    });
    
    const data = await response.json();
    if (data.success && data.user) {
      await chrome.storage.local.set({ 
        user_info: data.user,
        user_info_timestamp: now
      });
      return data.user;
    }
  } catch (error) {
    Logger.error('Error fetching user info:', error);
  }
  return null;
}

// 检查登录状态（这个应该立即响应）
async function checkLoginStatus() {
  try {
    const cookie = await chrome.cookies.get({
      url: COOKIE_URL,
      name: 'xToken'
    });
    
    const newLoginState = !!cookie;
    
    // 只有当登录状态发生变化时才广播
    if (newLoginState !== isLoggedIn) {
      isLoggedIn = newLoginState;
      // 广播登录状态变化
      chrome.runtime.sendMessage({ 
        type: 'loginStatusChanged', 
        isLoggedIn, 
        token: cookie?.value 
      });
      
      // 如果登出了，清除用户信息
      if (!isLoggedIn) {
        await chrome.storage.local.remove('user_info');
      }
      // 如果登录了，立即获取用户信息（强制更新）
      else {
        await fetchUserInfo(cookie.value, true);
      }
    }
    
    return isLoggedIn;
  } catch (error) {
    Logger.error('Error checking login status:', error);
    return false;
  }
}

// 统一的登录状态检查函数
async function checkAndUpdateLoginStatus() {
  const loginStatus = await checkLoginStatus();
  if (loginStatus) {
    const cookie = await chrome.cookies.get({
      url: COOKIE_URL,
      name: 'xToken'
    });
    if (cookie?.value) {
      await fetchUserInfo(cookie.value);
    }
  }
  return loginStatus;
}

// Constants
const STORAGE_KEYS = {
  SETTINGS: 'formify_settings',
  API_KEYS: 'formify_api_keys',
  VALIDATED_KEYS: 'formify_validated_keys'
};

// Provider Models Configuration
const PROVIDER_MODELS = {
  openai: [
    { id: 'gpt-4', name: 'GPT-4' },
    { id: 'gpt-4-turbo-preview', name: 'GPT-4 Turbo' },
    { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo' }
  ],
  claude: [
    { id: 'claude-3-opus-20240229', name: 'Claude-3 Opus' },
    { id: 'claude-3-sonnet-20240229', name: 'Claude-3 Sonnet' },
    { id: 'claude-2.1', name: 'Claude-2.1' }
  ],
  moonshot: [
    { id: 'moonshot-v1-8k', name: 'Moonshot V1 (8K)' },
    { id: 'moonshot-v1-32k', name: 'Moonshot V1 (32K)' },
    { id: 'moonshot-v1-128k', name: 'Moonshot V1 (128K)' }
  ],
  gemini: [
    { id: 'gemini-pro', name: 'Gemini Pro' }
  ]
};

// Listen for extension installation or update
chrome.runtime.onInstalled.addListener(async (details) => {
  if (details.reason === 'install') {
    Logger.info('Extension installed');
    // Initialize settings if needed
    chrome.storage.sync.get(STORAGE_KEYS.SETTINGS, (data) => {
      if (!data[STORAGE_KEYS.SETTINGS]) {
        const defaultProvider = 'openai';
        const defaultModels = {
          openai: 'gpt-3.5-turbo',
          claude: 'claude-3-sonnet-20240229',
          moonshot: 'moonshot-v1-32k',
          gemini: 'gemini-pro',
          deepseek: 'deepseek-chat',
          openrouter: 'openai/gpt-3.5-turbo',
          ollama: 'llama3.2:3b'
        };

        chrome.storage.sync.set({
          [STORAGE_KEYS.SETTINGS]: {
            useCustomApi: true,
            defaultProvider: defaultProvider,
            defaultModel: defaultModels[defaultProvider]
          }
        });
      }
    });
    
    // 检查初始登录状态
    await checkAndUpdateLoginStatus();
    // 首次安装时打开 onboarding 页面
    chrome.tabs.create({
      url: chrome.runtime.getURL('onboarding.html')
    });
  } else if (details.reason === 'update') {
    Logger.info('Extension updated');
    // 更新时不打开 onboarding 页面，只更新必要的设置
    chrome.storage.sync.get(STORAGE_KEYS.SETTINGS, (data) => {
      if (!data[STORAGE_KEYS.SETTINGS]) {
        const defaultProvider = 'openai';
        const defaultModels = {
          openai: 'gpt-3.5-turbo',
          claude: 'claude-3-sonnet-20240229',
          moonshot: 'moonshot-v1-32k',
          gemini: 'gemini-pro',
          deepseek: 'deepseek-chat',
          openrouter: 'openai/gpt-3.5-turbo',
          ollama: 'llama3.2:3b'
        };

        chrome.storage.sync.set({
          [STORAGE_KEYS.SETTINGS]: {
            useCustomApi: true,
            defaultProvider: defaultProvider,
            defaultModel: defaultModels[defaultProvider]
          }
        });
      }
    });
  }
});

// Listen for messages from content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  Logger.info('Received message:', request);
  
  if (request.type === 'getLoginStatus') {
    checkLoginStatus().then(status => {
      sendResponse({ isLoggedIn: status });
    });
    return true;
  }
  
  if (request.type === 'getUserInfo') {
    // 当 popup 或其他组件需要用户信息时
    chrome.cookies.get({
      url: COOKIE_URL,
      name: 'xToken'
    }).then(async (cookie) => {
      if (cookie?.value) {
        const userInfo = await fetchUserInfo(cookie.value);
        sendResponse({ success: true, user: userInfo });
      } else {
        sendResponse({ success: false });
      }
    });
    return true;
  }

  // 处理 AI 请求
  if (request.type === 'aiRequest') {
    handleAiRequest(request).then(response => {
      sendResponse(response);
    });
    return true;
  }
  
  if (request.action === 'getSettings') {
    chrome.storage.sync.get([STORAGE_KEYS.SETTINGS, STORAGE_KEYS.API_KEYS], (data) => {
      sendResponse({
        settings: data[STORAGE_KEYS.SETTINGS] || {},
        apiKeys: data[STORAGE_KEYS.API_KEYS] || {}
      });
    });
    return true;
  }
  
  if (request.action === 'updateSettings') {
    chrome.storage.sync.set({
      formify_settings: request.settings
    }, () => {
      sendResponse({ success: true });
    });
    return true;
  }
});

// Handle AI request
async function handleAiRequest(request) {
  try {
    // 获取当前设置
    const storage = await chrome.storage.sync.get([
      STORAGE_KEYS.SETTINGS,
      STORAGE_KEYS.API_KEYS,
      STORAGE_KEYS.VALIDATED_KEYS
    ]);

    const settings = storage[STORAGE_KEYS.SETTINGS] || {};
    const apiKeys = storage[STORAGE_KEYS.API_KEYS] || {};
    const validatedKeys = storage[STORAGE_KEYS.VALIDATED_KEYS] || {};

    // 获取提供商
    const provider = settings.defaultProvider || 'openai';

    // 检查提供商是否已验证
    if (!validatedKeys[provider]) {
      return {
        success: false,
        error: `No validated API key for ${provider}`
      };
    }

    // 根据提供商获取默认模型
    const defaultModels = {
      openai: 'gpt-3.5-turbo',
      claude: 'claude-3-sonnet-20240229',
      moonshot: 'moonshot-v1-32k',
      gemini: 'gemini-pro',
      deepseek: 'deepseek-chat',
      openrouter: 'openai/gpt-3.5-turbo',
      ollama: 'llama3.2:3b'
    };

    // 准备请求数据
    const requestData = {
      provider: provider,
      model: settings.defaultModel || defaultModels[provider],
      prompt: request.prompt,
      apiKey: apiKeys[provider],
      description: request.options.description,
      formFields: request.options.formFields,
      mode: request.options.mode,
      language: request.options.language,
      projectId: request.options.projectId,
      useCustomApi: settings.useCustomApi
    };

    // 如果是 Ollama，直接调用本地 API
    if (provider === 'ollama') {
      return await handleOllamaRequest(requestData);
    }

    // 对于其他提供商，发送请求到后端
    Logger.info('Sending AI request:', requestData);
    const response = await fetch(`${API_BASE_URL}/v1/form/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    Logger.success('AI request successful:', data);

    return {
      success: true,
      data: data
    };

  } catch (error) {
    Logger.error('Error in AI request:', error);
    return {
      success: false,
      error: error.message || 'Failed to process AI request'
    };
  }
}

// Handle Ollama request locally
async function handleOllamaRequest(requestData) {
  try {
    const endpoint = requestData.apiKey || 'http://localhost:11434';
    const cleanEndpoint = endpoint.endsWith('/') ? endpoint.slice(0, -1) : endpoint;

    Logger.info('Making Ollama request:', { endpoint: cleanEndpoint, model: requestData.model });

    // 首先检查 Ollama 服务是否可用
    try {
      const healthCheck = await fetch(`${cleanEndpoint}/api/tags`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!healthCheck.ok) {
        throw new Error(`Ollama service not available. Status: ${healthCheck.status}`);
      }
    } catch (healthError) {
      Logger.error('Ollama health check failed:', healthError);
      throw new Error('Ollama service is not running or not accessible. Please ensure Ollama is started and accessible at ' + cleanEndpoint);
    }

    const response = await fetch(`${cleanEndpoint}/api/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: requestData.model,
        prompt: requestData.prompt,
        stream: false
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      Logger.error('Ollama API error response:', errorText);
      throw new Error(`Ollama API error! status: ${response.status}, response: ${errorText}`);
    }

    const data = await response.json();
    Logger.success('Ollama request successful:', data);

    // 格式化响应以匹配预期的结构
    return {
      success: true,
      data: {
        content: data.response || data.text || 'No response from Ollama',
        usage: {
          prompt_tokens: 0,
          completion_tokens: 0,
          total_tokens: 0
        }
      }
    };

  } catch (error) {
    Logger.error('Error in Ollama request:', error);
    return {
      success: false,
      error: error.message || 'Failed to process Ollama request'
    };
  }
}

// 监听cookie变化
chrome.cookies.onChanged.addListener(async (changeInfo) => {
  const { cookie, removed, cause } = changeInfo;
  Logger.info('Cookie changed:', { cookie, removed, cause });
  
  if (cookie.domain.includes('fillify-343190162770.asia-east1.run.app') && cookie.name === 'xToken') {
    Logger.info('xToken cookie changed:', { value: cookie.value, removed });
    // 当 cookie 变化时，立即检查登录状态并强制更新用户信息
    const status = await checkLoginStatus();
    if (status && !removed) {
      await fetchUserInfo(cookie.value, true);
    }
  }
});
