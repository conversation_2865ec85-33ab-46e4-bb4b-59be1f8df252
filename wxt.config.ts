import { defineConfig } from 'wxt';

// See https://wxt.dev/api/config.html
export default defineConfig({
  extensionApi: 'chrome',
  modules: ['@wxt-dev/module-vue'],
  manifest: {
    name: 'Fillify - AI Form Filler with DeepSeek, OpenAI, Claude, Gemini, and More',
    version: '1.0.6',
    description: 'AI-Powered Forms, Emails & Bug Reports Assistant',
    permissions: [
      'storage',
      'tabs',
      'activeTab',
      'cookies'
    ],
    host_permissions: [
      'https://api.openai.com/*',
      'https://api.anthropic.com/*',
      'https://api.moonshot.cn/*',
      'https://api.deepseek.com/*',
      'https://generativelanguage.googleapis.com/*',
      'https://openrouter.ai/*',
      'https://fillify.tech/*'
    ],
    icons: {
      16: '/icon/icon16.png',
      32: '/icon/icon32.png',
      48: '/icon/icon48.png',
      128: '/icon/icon128.png'
    },
    action: {
      default_icon: {
        16: '/icon/icon16.png',
        32: '/icon/icon32.png',
        48: '/icon/icon48.png',
        128: '/icon/icon128.png'
      }
    },
    options_ui: {
      page: 'settings.html',
      open_in_tab: true
    }
  },
  entrypointsDir: './entrypoints'
});
