// Storage keys
const STORAGE_KEYS = {
  API_KEYS: 'formify_api_keys',
  SETTINGS: 'formify_settings',
  PROJECTS: 'formify_projects',
  TOKEN_STATS: 'formify_token_stats',
  CUSTOM_PROVIDERS: 'formify_custom_providers',
  FIRST_VISIT: 'formify_first_visit',
  COLLAPSED_SECTIONS: 'formify_collapsed_sections',
  ACTIVE_TAB: 'formify_active_tab',
  VALIDATED_KEYS: 'formify_validated_keys'
};

// UI Elements
const elements = {
  apiKeys: {
    openai: document.getElementById('openai-key'),
    moonshot: document.getElementById('moonshot-key'),
    claude: document.getElementById('claude-key'),
    gemini: document.getElementById('gemini-key'),
    deepseek: document.getElementById('deepseek-key')
  },
  settings: {
    'use-custom-api': document.getElementById('use-custom-api'),
    'default-provider': document.getElementById('default-provider'),
    'default-model': document.getElementById('default-model')
  },
  aiProvidersSection: document.getElementById('ai-providers-section'),
  projectsContainer: document.getElementById('projects-container'),
  projectModal: document.getElementById('project-modal'),
  projectForm: document.getElementById('project-form'),
  projectName: document.getElementById('project-name'),
  projectInfo: document.getElementById('project-info'),
  projectEnvironment: document.getElementById('project-environment'),
  bugReportTemplate: document.getElementById('bug-report-template'),
  closeModal: document.querySelector('.modal-close'),
  cancelBtn: document.querySelector('.cancel-btn'),
  saveBtn: document.querySelector('.save-btn')
};

let currentEditingProject = null;

// Modal variables
let editingProjectId = null;
const projectModal = document.getElementById('project-modal');
const modalForm = projectModal?.querySelector('.modal-form');

// API URLs
const API_BASE_URL = 'https://fillify.tech/api';

// Provider Models Configuration
const PROVIDER_MODELS = {
  openai: [
    { id: 'gpt-4', name: 'GPT-4' },
    { id: 'gpt-4-turbo-preview', name: 'GPT-4 Turbo' },
    { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo' }
  ],
  claude: [
    { id: 'claude-3-opus-20240229', name: 'Claude-3 Opus' },
    { id: 'claude-3-sonnet-20240229', name: 'Claude-3 Sonnet' },
    { id: 'claude-2.1', name: 'Claude-2.1' }
  ],
  moonshot: [
    { id: 'moonshot-v1-8k', name: 'Moonshot V1 (8K)' },
    { id: 'moonshot-v1-32k', name: 'Moonshot V1 (32K)' },
    { id: 'moonshot-v1-128k', name: 'Moonshot V1 (128K)' }
  ],
  gemini: [
    { id: 'gemini-pro', name: 'Gemini Pro' }
  ],
  deepseek: [
    { id: 'deepseek-chat', name: 'DeepSeek Chat' },
    { id: 'deepseek-reasoner', name: 'DeepSeek Reasoner' }
  ]
};

// Update model options based on selected provider
function updateModelOptions(provider, selectedModel) {
  const modelSelect = elements.settings['default-model'];
  if (!modelSelect) return;

  // 获取已验证的API keys
  chrome.storage.sync.get([STORAGE_KEYS.VALIDATED_KEYS], (storage) => {
    const validatedKeys = storage[STORAGE_KEYS.VALIDATED_KEYS] || {};

    // 更新provider选项的禁用状态
    const providerSelect = elements.settings['default-provider'];
    if (providerSelect) {
      Array.from(providerSelect.options).forEach(option => {
        const providerName = option.value;
        option.disabled = providerName && !validatedKeys[providerName];
      });
    }

    // 清空现有选项
    modelSelect.innerHTML = '';

    // 只有当服务商已验证时才显示其模型列表
    if (validatedKeys[provider]) {
      // 根据选择的provider添加相应的模型选项
      const models = PROVIDER_MODELS[provider] || [];
      models.forEach(model => {
        const option = document.createElement('option');
        option.value = model.id;
        option.textContent = model.name;
        modelSelect.appendChild(option);
      });

      // 如果有已选择的模型，设置为选中状态
      if (selectedModel) {
        modelSelect.value = selectedModel;
      }
    }
  });
}

// Initialize API providers
async function initializeApiProviders() {
  try {
    const storage = await chrome.storage.sync.get([
      STORAGE_KEYS.API_KEYS,
      STORAGE_KEYS.SETTINGS,
      STORAGE_KEYS.VALIDATED_KEYS
    ]);

    const apiKeys = storage[STORAGE_KEYS.API_KEYS] || {};
    const settings = storage[STORAGE_KEYS.SETTINGS] || {};
    const validatedKeys = storage[STORAGE_KEYS.VALIDATED_KEYS] || {};

    // 初始化 API key 输入框
    Object.entries(elements.apiKeys).forEach(([provider, input]) => {
      if (input && apiKeys[provider]) {
        input.value = apiKeys[provider];
        if (validatedKeys[provider]) {
          input.classList.add('valid');
        }
      }
    });

    // 初始化设置前先检查登录状态
    const response = await chrome.runtime.sendMessage({ type: 'getLoginStatus' });
    const isLoggedIn = response && response.isLoggedIn;

    // 初始化 use-custom-api 设置
    if (elements.settings['use-custom-api']) {
      // 如果未登录且 storage 中的设置是 false，则强制设为 true
      if (!isLoggedIn && !settings.useCustomApi) {
        settings.useCustomApi = true;
        // 更新 storage 中的设置
        await chrome.storage.sync.set({
          [STORAGE_KEYS.SETTINGS]: settings
        });
      }
      elements.settings['use-custom-api'].checked = settings.useCustomApi !== false; // 默认为 true
    }

    if (elements.settings['default-provider']) {
      const defaultProvider = settings.defaultProvider || 'openai';
      elements.settings['default-provider'].value = defaultProvider;
      // 初始化模型选项
      updateModelOptions(defaultProvider, settings.defaultModel);
    }

    // 设置事件监听器
    setupSettingsListeners();

  } catch (error) {
    console.error('Error initializing API providers:', error);
    showNotification('Failed to initialize API providers', 'error');
  }
}

// Setup settings listeners
function setupSettingsListeners() {
  // 监听设置变化
  Object.entries(elements.settings).forEach(([key, element]) => {
    if (!element) return;

    if (key === 'use-custom-api') {
      let isProcessingLoginCheck = false;

      element.addEventListener('change', async (event) => {
        try {
          // 如果正在处理登录检查，不要继续处理
          if (isProcessingLoginCheck) {
            return;
          }

          // 如果用户要关闭 Custom API
          if (!event.target.checked) {
            isProcessingLoginCheck = true;
            // 检查登录状态
            const response = await chrome.runtime.sendMessage({ type: 'getLoginStatus' });
            if (!response || !response.isLoggedIn) {
              // 如果未登录，恢复选中状态并显示登录确认框
              event.target.checked = true;
              showLoginConfirmModal();
              isProcessingLoginCheck = false;
              // 确保 storage 中的设置也保持为 true
              const storage = await chrome.storage.sync.get(STORAGE_KEYS.SETTINGS);
              const settings = storage[STORAGE_KEYS.SETTINGS] || {};
              settings.useCustomApi = true;
              await chrome.storage.sync.set({
                [STORAGE_KEYS.SETTINGS]: settings
              });
              return;
            }
            isProcessingLoginCheck = false;
          }

          // 获取当前设置
          const storage = await chrome.storage.sync.get(STORAGE_KEYS.SETTINGS);
          const settings = storage[STORAGE_KEYS.SETTINGS] || {};

          // 更新设置
          settings.useCustomApi = event.target.checked;

          // 保存设置
          await chrome.storage.sync.set({
            [STORAGE_KEYS.SETTINGS]: settings
          });

          showNotification('Settings saved successfully', 'success');
        } catch (error) {
          console.error('Error saving settings:', error);
          showNotification('Failed to save settings', 'error');
          // 如果保存失败，恢复到之前的状态
          event.target.checked = !event.target.checked;
          isProcessingLoginCheck = false;
        }
      });
    } else if (key === 'default-provider') {
      // 添加 provider 变更处理
      element.addEventListener('change', async () => {
        const provider = element.value;
        updateModelOptions(provider);

        try {
          const storage = await chrome.storage.sync.get(STORAGE_KEYS.SETTINGS);
          const settings = storage[STORAGE_KEYS.SETTINGS] || {};
          settings.defaultProvider = provider;
          settings.defaultModel = elements.settings['default-model']?.value;

          await chrome.storage.sync.set({
            [STORAGE_KEYS.SETTINGS]: settings
          });

          showNotification('Settings saved successfully', 'success');
        } catch (error) {
          console.error('Error saving settings:', error);
          showNotification('Failed to save settings', 'error');
        }
      });
    } else if (key === 'default-model') {
      // 添加模型变更处理
      element.addEventListener('change', async () => {
        try {
          const storage = await chrome.storage.sync.get(STORAGE_KEYS.SETTINGS);
          const settings = storage[STORAGE_KEYS.SETTINGS] || {};
          settings.defaultModel = element.value;

          await chrome.storage.sync.set({
            [STORAGE_KEYS.SETTINGS]: settings
          });

          showNotification('Settings saved successfully', 'success');
        } catch (error) {
          console.error('Error saving settings:', error);
          showNotification('Failed to save settings', 'error');
        }
      });
    } else {
      // 其他设置的原有处理逻辑
      element.addEventListener('change', async () => {
        try {
          const storage = await chrome.storage.sync.get(STORAGE_KEYS.SETTINGS);
          const settings = storage[STORAGE_KEYS.SETTINGS] || {};

          if (element.type === 'checkbox') {
            settings[key] = element.checked;
          } else if (element.type === 'number') {
            settings[key] = parseInt(element.value) || 3;
          } else {
            settings[key] = element.value;
          }

          await chrome.storage.sync.set({
            [STORAGE_KEYS.SETTINGS]: settings
          });

          showNotification('Settings saved successfully', 'success');
        } catch (error) {
          console.error('Error saving settings:', error);
          showNotification('Failed to save settings', 'error');
        }
      });
    }
  });

  // 特殊处理 use-custom-api 的变化
  const useCustomApi = elements.settings['use-custom-api'];
  if (useCustomApi) {
    useCustomApi.addEventListener('change', function() {
          const aiProvidersSection = elements.aiProvidersSection;
      if (!aiProvidersSection) return;

      const sectionContent = aiProvidersSection.querySelector('.card-content');
      if (this.checked && sectionContent) {
              sectionContent.style.display = 'block';
      }
    });
  }
}

// Setup visibility toggles for API keys
function setupVisibilityToggles() {
  document.querySelectorAll('.visibility-toggle').forEach(button => {
    button.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();

      const input = button.parentElement.querySelector('.key-input');
      if (!input) return;

      // 切换输入框类型
      input.type = input.type === 'password' ? 'text' : 'password';
    });
  });
}

// Initialize settings
async function initializeSettings() {
  try {
    const storage = await chrome.storage.sync.get([
      STORAGE_KEYS.SETTINGS,
      STORAGE_KEYS.COLLAPSED_SECTIONS
    ]);

    const settings = storage[STORAGE_KEYS.SETTINGS] || {};
    const collapsedSections = storage[STORAGE_KEYS.COLLAPSED_SECTIONS] || {};

    // 初始化折叠状态
    document.querySelectorAll('.settings-card').forEach(card => {
      const cardId = card.id;
      if (!cardId) return;

      const content = card.querySelector('.card-content');
      const collapseButton = card.querySelector('.collapse-button');

      if (content && collapseButton) {
        if (collapsedSections[cardId]) {
          content.style.display = 'none';
          collapseButton.classList.add('collapsed');
        }

        collapseButton.addEventListener('click', async () => {
          const isCollapsed = collapseButton.classList.toggle('collapsed');
          content.style.display = isCollapsed ? 'none' : 'block';

          // 保存折叠状态
          collapsedSections[cardId] = isCollapsed;
          await chrome.storage.sync.set({
            [STORAGE_KEYS.COLLAPSED_SECTIONS]: collapsedSections
          });
        });
      }
    });

    // 初始化其他设置
    if (elements.settings['default-provider']) {
      const provider = settings.defaultProvider || 'openai';
      elements.settings['default-provider'].value = provider;

      // 初始化模型选项并设置保存的值
      updateModelOptions(provider, settings.defaultModel);
    }

  } catch (error) {
    console.error('Error initializing settings:', error);
    showNotification('Failed to initialize settings', 'error');
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
  try {
    // Handle URL parameters first
    const urlParams = new URLSearchParams(window.location.search);
    const action = urlParams.get('action');

    // 设置 tab 切换逻辑
    setupTabNavigation();

    // Initialize API providers first
    await initializeApiProviders();

    // Initialize settings
    await initializeSettings();

    // Initialize projects
    await initializeProjects();

    // Load token stats
    await loadTokenStats();
    initializeResetStats();

    // If action is add_project, switch to library tab and open modal
    if (action === 'add_project') {
      console.log('Opening add project modal from URL parameter');
      // 切换到 Library 标签页
      const libraryTab = document.querySelector('[data-tab="library"]');
      if (libraryTab) {
        const event = new Event('click');
        libraryTab.dispatchEvent(event);
      }
      // 打开 modal - 使用 Vue 的方式
      setTimeout(() => {
        // 尝试通过 Vue 的方式打开模态弹窗
        window.openProjectModalVue && window.openProjectModalVue();
      }, 100);
    }

    // Initialize login confirm modal
    //initializeLoginConfirmModal();

  } catch (error) {
    console.error('Error initializing page:', error);
    showNotification('Failed to initialize settings', 'error');
  }
});

// Tab 切换功能
function setupTabNavigation() {
  const tabButtons = document.querySelectorAll('.nav-item');
  const tabContents = document.querySelectorAll('.tab-content');

  tabButtons.forEach(button => {
    button.addEventListener('click', () => {
      // 移除所有 active 类
      tabButtons.forEach(btn => btn.classList.remove('active'));
      tabContents.forEach(content => content.classList.remove('active'));

      // 添加 active 类到当前选中的 tab
      button.classList.add('active');
      const tabId = button.dataset.tab;
      document.getElementById(`${tabId}-tab`).classList.add('active');

      // 保存当前激活的标签页
      chrome.storage.sync.set({
        [STORAGE_KEYS.ACTIVE_TAB]: tabId
    });
  });
  });

  // 恢复上次激活的标签页
  chrome.storage.sync.get(STORAGE_KEYS.ACTIVE_TAB, (result) => {
    const lastActiveTab = result[STORAGE_KEYS.ACTIVE_TAB];
    if (lastActiveTab) {
      const tabButton = document.querySelector(`.nav-item[data-tab="${lastActiveTab}"]`);
      if (tabButton) {
        tabButton.click();
      }
    }
  });
}

// Initialize project management
async function initializeProjects() {
  if (!projectModal || !modalForm) {
    console.error('Project modal or form not found');
    return;
  }

  const addProjectBtn = document.getElementById('add-project-btn');
  const cancelBtn = document.getElementById('cancel-project');
  const closeBtn = projectModal?.querySelector('.modal-close');
  const saveBtn = document.getElementById('save-project');

  // 绑定事件监听器
  addProjectBtn?.addEventListener('click', () => openModal());
  cancelBtn?.addEventListener('click', closeModal);
  closeBtn?.addEventListener('click', closeModal);
  saveBtn?.addEventListener('click', async (e) => {
    e.preventDefault();
    await saveProject();
  });

  // 阻止表单默认提交
  modalForm?.addEventListener('submit', async (e) => {
    e.preventDefault();
    await saveProject();
  });

  // 点击模态框外部关闭
  projectModal.addEventListener('click', (event) => {
    if (event.target === projectModal) {
      closeModal();
    }
  });

  // 初始加载项目列表
  await loadProjects();
}

// Initialize DOM elements
const initializeElements = () => {
  // API Key inputs
  ['openai', 'claude', 'moonshot', 'gemini'].forEach(provider => {
    const input = document.getElementById(`${provider}-key`);
    if (input) {
      elements.apiKeys[provider] = input;
    }
  });

  // Settings elements
  ['use-custom-api', 'default-provider', 'default-model'].forEach(id => {
    const element = document.getElementById(id);
    if (element) {
      elements.settings[id] = element;
    }
  });
};

// Initialize tabs
const initializeTabs = async () => {
  const tabButtons = document.querySelectorAll('.nav-item');
  const tabContents = document.querySelectorAll('.tab-content');

  // 从存储中获取上次激活的标签页
  const storage = await chrome.storage.sync.get(STORAGE_KEYS.ACTIVE_TAB);
  const lastActiveTab = storage[STORAGE_KEYS.ACTIVE_TAB];

  // 设置初始激活状态
  tabButtons.forEach(button => {
    const tabId = button.getAttribute('data-tab');
    const isActive = lastActiveTab ? tabId === lastActiveTab : tabId === 'general';
    button.classList.toggle('active', isActive);

    // 添加点击事件
    button.addEventListener('click', async () => {
      // 移除所有激活状态
      tabButtons.forEach(btn => btn.classList.remove('active'));
      tabContents.forEach(content => content.classList.remove('active'));

      // 设置新的激活状态
      button.classList.add('active');
      const content = document.getElementById(`${tabId}-tab`);
      if (content) {
        content.classList.add('active');
      }

      // 保存当前激活的标签页
      await chrome.storage.sync.set({
        [STORAGE_KEYS.ACTIVE_TAB]: tabId
    });
  });
  });

  // 设置初始内容显示
  tabContents.forEach(content => {
    const tabId = content.id.replace('-tab', '');
    const isActive = lastActiveTab ? tabId === lastActiveTab : tabId === 'general';
    content.classList.toggle('active', isActive);
  });
};

// Initialize collapse functionality
const initializeCollapse = async () => {
  const storage = await chrome.storage.sync.get(STORAGE_KEYS.COLLAPSED_SECTIONS);
  const collapsedSections = storage[STORAGE_KEYS.COLLAPSED_SECTIONS] || {};

  document.querySelectorAll('.collapse-button').forEach(button => {
    const card = button.closest('.settings-card');
    if (!card) return;

    const cardId = card.id;
    const content = card.querySelector('.card-content');
    if (!content) return;

    // 设置初始状态
    if (collapsedSections[cardId]) {
      content.style.display = 'none';
      button.classList.add('collapsed');
    }

    // 添加点击事件
    button.addEventListener('click', async () => {
      const isCollapsed = button.classList.toggle('collapsed');
      content.style.display = isCollapsed ? 'none' : 'block';

      // 保存折叠状态
      collapsedSections[cardId] = isCollapsed;
      await chrome.storage.sync.set({
        [STORAGE_KEYS.COLLAPSED_SECTIONS]: collapsedSections
      });
    });
  });
};

// Load API keys from storage
const loadApiKeys = async () => {
  try {
    const storage = await chrome.storage.sync.get([
      STORAGE_KEYS.API_KEYS,
      STORAGE_KEYS.SETTINGS,
      STORAGE_KEYS.VALIDATED_KEYS
    ]);

    const apiKeys = storage[STORAGE_KEYS.API_KEYS] || {};
    const settings = storage[STORAGE_KEYS.SETTINGS] || {};
    const validatedKeys = storage[STORAGE_KEYS.VALIDATED_KEYS] || {};

    // 初始化 API key 输入框
    Object.entries(elements.apiKeys).forEach(([provider, input]) => {
      if (input && apiKeys[provider]) {
        input.value = apiKeys[provider];
        if (validatedKeys[provider]) {
          input.classList.add('valid');
        }
      }
    });

    // 初始化设置
    if (elements.settings['use-custom-api']) {
      elements.settings['use-custom-api'].checked = settings.useCustomApi || false;
    }
    if (elements.settings['default-provider']) {
      elements.settings['default-provider'].value = settings.defaultProvider || 'openai';
    }
  } catch (error) {
    console.error('Error loading API keys:', error);
    showNotification('Failed to load API keys', 'error');
  }
};

// Save settings to storage
const saveSettings = async (event) => {
  try {
    const storage = await chrome.storage.sync.get([STORAGE_KEYS.API_KEYS, STORAGE_KEYS.SETTINGS, STORAGE_KEYS.VALIDATED_KEYS]);
    const apiKeys = storage[STORAGE_KEYS.API_KEYS] || {};
    const settings = storage[STORAGE_KEYS.SETTINGS] || {};
    const validatedKeys = storage[STORAGE_KEYS.VALIDATED_KEYS] || {};

    // 如果是 API key 输入框触发的保存
    if (event?.target?.classList.contains('key-input')) {
      const provider = event.target.id.replace('-key', '');
      const newValue = event.target.value.trim();

      // 如果输入为空，删除该 API key
      if (!newValue) {
        delete apiKeys[provider];
        delete validatedKeys[provider];
        event.target.classList.remove('valid', 'invalid');
      } else {
        apiKeys[provider] = newValue;
      }
    }

    // 更新设置，但跳过 use-custom-api 的处理（因为它有自己的专门处理逻辑）
    if (elements.settings['default-provider']) {
      settings.defaultProvider = elements.settings['default-provider'].value || 'openai';
    }
    if (elements.settings['default-model']) {
      settings.defaultModel = elements.settings['default-model'].value || 'gpt-3.5-turbo';
    }

    // 保存所有更改
    await chrome.storage.sync.set({
      [STORAGE_KEYS.API_KEYS]: apiKeys,
      [STORAGE_KEYS.SETTINGS]: settings,
      [STORAGE_KEYS.VALIDATED_KEYS]: validatedKeys
    });

  } catch (error) {
    console.error('Error saving settings:', error);
  }
};

// Validate API key
async function validateApiKey(provider, key) {
  const input = document.getElementById(`${provider}-key`);
  if (!input) return false;

  const container = input.closest('.key-input-group');
  const loadingSpinner = container?.querySelector('.loading-spinner');

  try {
    if (loadingSpinner) {
      loadingSpinner.classList.add('active');
    }

    input.classList.remove('valid', 'invalid');

    // 验证 API key
    const isValid = await testApiKey(provider, key);

    // 更新 UI 和存储
    input.classList.add(isValid ? 'valid' : 'invalid');

    const storage = await chrome.storage.sync.get([
      STORAGE_KEYS.VALIDATED_KEYS,
      STORAGE_KEYS.API_KEYS,
      STORAGE_KEYS.SETTINGS
    ]);
    const validatedKeys = storage[STORAGE_KEYS.VALIDATED_KEYS] || {};
    const apiKeys = storage[STORAGE_KEYS.API_KEYS] || {};
    const settings = storage[STORAGE_KEYS.SETTINGS] || {};

    if (isValid) {
      validatedKeys[provider] = key;
      apiKeys[provider] = key;

      // 检查是否是第一个有效的 API key
      const hasOtherValidKeys = Object.keys(validatedKeys).some(p => p !== provider);
      if (!hasOtherValidKeys) {
        // 如果是第一个有效的 key，设置为默认提供商
        settings.defaultProvider = provider;
        if (elements.settings['default-provider']) {
          elements.settings['default-provider'].value = provider;
          // 更新模型选项
          updateModelOptions(provider);
        }
      }

      showNotification(`${provider.toUpperCase()} API key validated successfully`, 'success');
    } else {
      delete validatedKeys[provider];
      delete apiKeys[provider];
      showNotification(`Invalid ${provider.toUpperCase()} API key`, 'error');
    }

    await chrome.storage.sync.set({
      [STORAGE_KEYS.VALIDATED_KEYS]: validatedKeys,
      [STORAGE_KEYS.API_KEYS]: apiKeys,
      [STORAGE_KEYS.SETTINGS]: settings
    });

    return isValid;
  } catch (error) {
    // Error validating API key
    return false;
  } finally {
    if (loadingSpinner) {
      loadingSpinner.classList.remove('active');
    }
  }
}

// Test API key
async function testApiKey(provider, key) {
  const endpoints = {
    openai: 'https://api.openai.com/v1/models',
    claude: 'https://api.anthropic.com/v1/models',
    moonshot: 'https://api.moonshot.cn/v1/models',
    gemini: `https://generativelanguage.googleapis.com/v1/models?key=${key}`,
    deepseek: 'https://api.deepseek.com/v1/models'
  };

  try {
    const headers = {
      'Content-Type': 'application/json'
    };

    // 根据不同的提供商设置不同的认证头
    if (provider === 'claude') {
      headers['x-api-key'] = key;
      headers['anthropic-version'] = '2023-06-01';
    } else if (provider === 'gemini') {
      // Gemini 不需要额外的认证头，key 已经在 URL 中
    } else {
      headers['Authorization'] = `Bearer ${key}`;
    }

    const response = await fetch(endpoints[provider], {
      method: 'GET',
      headers: headers
    });

    return response.ok;
  } catch (error) {
    // Error testing API key
    return false;
  }
}

// Show notification
const showNotification = (message, type = 'info') => {
  // 移除现有的通知
  const existingNotification = document.querySelector('.notification');
  if (existingNotification) {
    existingNotification.remove();
  }

  // 创建新的通知
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.textContent = message;
  document.body.appendChild(notification);

  // 3秒后自动移除
  setTimeout(() => {
    notification.remove();
  }, 3000);
};

// Modal functions
const openModal = (project = null) => {
  if (!projectModal || !modalForm) {
    console.error('Project modal or form not found');
      return;
  }

  modalForm.reset();
  const titleEl = projectModal.querySelector('.modal-header h3');

  if (project) {
    // 编辑模式
    editingProjectId = project.id;
    titleEl.textContent = 'Edit Project';
    document.getElementById('project-name').value = project.name || '';
    document.getElementById('project-description').value = project.description || '';
    document.getElementById('project-environment').value = project.environment || '';
    document.getElementById('project-template').value = project.template || '';
  } else {
    // 添加模式
    editingProjectId = null;
    titleEl.textContent = 'Add Project';
  }

  projectModal.style.display = 'flex';
};

const closeModal = () => {
  if (!projectModal || !modalForm) return;
  projectModal.style.display = 'none';
  modalForm.reset();
  editingProjectId = null;
};

// Save project
const saveProject = async () => {
  const formData = new FormData(modalForm);
  const projectData = {
    id: editingProjectId || Date.now().toString(),
    name: formData.get('project-name') || '',
    description: formData.get('project-description') || '',
    environment: formData.get('project-environment') || '',
    template: formData.get('project-template') || '',
    updatedAt: new Date().toISOString()
  };

  // 验证必填字段
  if (!projectData.name) {
    showNotification('Project name is required', 'error');
    return;
  }

  try {
    const storage = await chrome.storage.sync.get(STORAGE_KEYS.PROJECTS);
    let projects = storage[STORAGE_KEYS.PROJECTS] || [];

    if (editingProjectId) {
      // 更新现有项目
      projects = projects.map(p => p.id === editingProjectId ? projectData : p);
    } else {
      // 添加新项目
      projectData.createdAt = projectData.updatedAt;
      projects.push(projectData);
    }

    await chrome.storage.sync.set({
      [STORAGE_KEYS.PROJECTS]: projects
    });

    closeModal();
    showNotification(editingProjectId ? 'Project updated successfully' : 'Project added successfully', 'success');
    await loadProjects();
  } catch (error) {
    console.error('Error saving project:', error);
    showNotification('Failed to save project', 'error');
  }
};

// Load projects
const loadProjects = async () => {
  try {
    const storage = await chrome.storage.sync.get([STORAGE_KEYS.PROJECTS, STORAGE_KEYS.SETTINGS]);
    const projects = storage[STORAGE_KEYS.PROJECTS] || [];
    const settings = storage[STORAGE_KEYS.SETTINGS] || {};
    const showDetails = settings.showProjectDetails || false;

    const projectList = document.getElementById('projects-container');
    if (!projectList) {
      console.error('Projects container not found');
      return;
    }

    // 清空现有项目列表
    projectList.innerHTML = '';

    if (projects.length === 0) {
      // 显示无项目提示
      const emptyMessage = document.createElement('div');
      emptyMessage.className = 'empty-message';
      emptyMessage.textContent = 'No projects added yet';
      projectList.appendChild(emptyMessage);
      return;
    }

    // 添加项目到列表
    projects.forEach(project => {
      const projectItem = document.createElement('div');
      projectItem.className = 'project-item';
      projectItem.innerHTML = `
        <div class="project-header">
          <h3>${project.name}</h3>
          <div class="project-actions">
            <button class="button-secondary edit-btn" data-id="${project.id}">Edit</button>
            <button class="button-danger delete-btn" data-id="${project.id}">Delete</button>
          </div>
        </div>
        <div class="project-details" style="display: ${showDetails ? 'block' : 'none'};">
          <p><strong>Description:</strong> ${project.description || 'No description'}</p>
          <p><strong>Environment:</strong> ${project.environment || 'No environment info'}</p>
          <p><strong>Template:</strong> ${project.template || 'No template'}</p>
        </div>
      `;

      // 添加项目点击事件（展开/折叠详情）
      const header = projectItem.querySelector('.project-header');
      const details = projectItem.querySelector('.project-details');
      if (header && details) {
        header.addEventListener('click', (event) => {
          // 忽略按钮点击
          if (event.target.tagName === 'BUTTON') return;
          details.style.display = details.style.display === 'none' ? 'block' : 'none';
        });
      }

      // 添加编辑按钮事件
      const editBtn = projectItem.querySelector('.edit-btn');
      if (editBtn) {
        editBtn.addEventListener('click', () => {
          openModal(project);
        });
      }

      // 添加删除按钮事件
      const deleteBtn = projectItem.querySelector('.delete-btn');
      if (deleteBtn) {
        deleteBtn.addEventListener('click', async () => {
          if (confirm('Are you sure you want to delete this project?')) {
            try {
              const projectId = deleteBtn.getAttribute('data-id');
              const storage = await chrome.storage.sync.get(STORAGE_KEYS.PROJECTS);
              const projects = storage[STORAGE_KEYS.PROJECTS] || [];

              // 删除项目
              const updatedProjects = projects.filter(p => p.id !== projectId);

    await chrome.storage.sync.set({
                [STORAGE_KEYS.PROJECTS]: updatedProjects
              });

              // 移除项目元素
              projectItem.remove();

              // 如果没有项目了，显示空提示
              if (updatedProjects.length === 0) {
                await loadProjects();
              }

              showNotification('Project deleted successfully', 'success');
  } catch (error) {
              console.error('Error deleting project:', error);
              showNotification('Failed to delete project', 'error');
            }
          }
        });
      }

      projectList.appendChild(projectItem);
    });
  } catch (error) {
    console.error('Error loading projects:', error);
    showNotification('Failed to load projects', 'error');
  }
};

// Load token stats
async function loadTokenStats() {
  try {
    const storage = await chrome.storage.sync.get(STORAGE_KEYS.TOKEN_STATS);
    const stats = storage[STORAGE_KEYS.TOKEN_STATS] || {};

    // 调试: 在控制台中显示原始统计数据
    console.log('Token stats raw data:', stats);

    const tokenGrid = document.querySelector('.token-grid');
    if (!tokenGrid) return;

    // 清空现有内容
    tokenGrid.innerHTML = '';

    // 检查是否有统计数据
    const providers = ['openai', 'claude', 'moonshot', 'gemini', 'deepseek'];
    const hasStats = providers.some(provider => stats[provider]?.totalTokens > 0);

    // 显示或隐藏重置按钮
    const resetBtn = document.getElementById('reset-stats');
    if (resetBtn) {
      resetBtn.style.display = hasStats ? 'block' : 'none';
    }

    if (!hasStats) {
      // 显示无数据提示
      tokenGrid.innerHTML = '<div class="empty-message">No token usage data available</div>';
      return;
    }

    // 创建统计卡片
    providers.forEach(provider => {
      const data = stats[provider] || {
        promptTokens: 0,
        completionTokens: 0,
        totalTokens: 0,
        lastUpdated: null
      };

      // 如果没有使用记录，跳过
      if (data.totalTokens === 0) return;

      const card = document.createElement('div');
      card.className = 'token-card';

      const lastUpdated = data.lastUpdated
        ? new Date(data.lastUpdated).toLocaleString()
        : 'Never';

      // 修改 provider 名称显示
      const providerDisplayNames = {
        'openai': 'OpenAI',
        'deepseek': 'DeepSeek',
        'claude': 'Claude',
        'gemini': 'Gemini',
        'moonshot': 'Moonshot'
      };

      card.innerHTML = `
        <div class="token-header">
          <h3>${providerDisplayNames[provider]}</h3>
        </div>
        <div class="token-stats">
          <div class="stat-item">
            <span class="stat-label">Prompt Tokens:</span>
            <span class="stat-value">${data.promptTokens.toLocaleString()}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Completion Tokens:</span>
            <span class="stat-value">${data.completionTokens.toLocaleString()}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Total Tokens:</span>
            <span class="stat-value">${data.totalTokens.toLocaleString()}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Last Updated:</span>
            <span class="stat-value">${lastUpdated}</span>
          </div>
        </div>
      `;

      tokenGrid.appendChild(card);
    });
  } catch (error) {
    // Error loading token stats
    showNotification('Failed to load token statistics', 'error');
  }
}

// Initialize reset stats functionality
function initializeResetStats() {
  const resetBtn = document.getElementById('reset-stats');
  if (!resetBtn) return;

  resetBtn.addEventListener('click', async () => {
    if (confirm('Are you sure you want to reset all token statistics? This action cannot be undone.')) {
      try {
        // 重置所有提供商的统计数据
        const emptyStats = {
          openai: { promptTokens: 0, completionTokens: 0, totalTokens: 0, lastUpdated: new Date().toISOString() },
          claude: { promptTokens: 0, completionTokens: 0, totalTokens: 0, lastUpdated: new Date().toISOString() },
          moonshot: { promptTokens: 0, completionTokens: 0, totalTokens: 0, lastUpdated: new Date().toISOString() },
          gemini: { promptTokens: 0, completionTokens: 0, totalTokens: 0, lastUpdated: new Date().toISOString() },
          deepseek: { promptTokens: 0, completionTokens: 0, totalTokens: 0, lastUpdated: new Date().toISOString() }
        };

        await chrome.storage.sync.set({ [STORAGE_KEYS.TOKEN_STATS]: emptyStats });
        await loadTokenStats();
        showNotification('Token statistics reset successfully', 'success');
  } catch (error) {
        console.error('Error resetting token stats:', error);
        showNotification('Failed to reset token statistics', 'error');
      }
    }
  });
}

// Initialize collapse functionality for token usage card
function initializeTokenUsageCollapse() {
  const tokenCard = document.getElementById('token-usage');
  if (!tokenCard) return;

  const collapseButton = tokenCard.querySelector('.collapse-button');
  const content = tokenCard.querySelector('.card-content');

  if (!collapseButton || !content) return;

  // 从存储中获取折叠状态
  chrome.storage.sync.get(STORAGE_KEYS.COLLAPSED_SECTIONS, (result) => {
    const collapsedSections = result[STORAGE_KEYS.COLLAPSED_SECTIONS] || {};

    if (collapsedSections['token-usage']) {
      content.style.display = 'none';
      collapseButton.classList.add('collapsed');
    }
  });

  collapseButton.addEventListener('click', async () => {
    const isCollapsed = collapseButton.classList.toggle('collapsed');
    content.style.display = isCollapsed ? 'none' : 'block';

    // 保存折叠状态
    const storage = await chrome.storage.sync.get(STORAGE_KEYS.COLLAPSED_SECTIONS);
    const collapsedSections = storage[STORAGE_KEYS.COLLAPSED_SECTIONS] || {};
    collapsedSections['token-usage'] = isCollapsed;

    await chrome.storage.sync.set({
      [STORAGE_KEYS.COLLAPSED_SECTIONS]: collapsedSections
    });
  });
}

// Initialize onboarding guide
const initializeOnboardingGuide = () => {
  const showOnboardingBtn = document.getElementById('show-onboarding');
  if (showOnboardingBtn) {
    showOnboardingBtn.addEventListener('click', () => {
      chrome.tabs.create({
        url: chrome.runtime.getURL('onboarding.html')
      });
    });
  }
};

// Initialize project details toggle
const initializeProjectDetails = () => {
  const showDetailsToggle = document.getElementById('show-project-details');
  if (!showDetailsToggle) return;

  // 加载设置
  chrome.storage.sync.get(STORAGE_KEYS.SETTINGS, (storage) => {
    const settings = storage[STORAGE_KEYS.SETTINGS] || {};
    showDetailsToggle.checked = settings.showProjectDetails || false;
  });

  // 添加变更事件
  showDetailsToggle.addEventListener('change', async () => {
    try {
  const storage = await chrome.storage.sync.get(STORAGE_KEYS.SETTINGS);
  const settings = storage[STORAGE_KEYS.SETTINGS] || {};

      // 更新设置
      settings.showProjectDetails = showDetailsToggle.checked;

  await chrome.storage.sync.set({
    [STORAGE_KEYS.SETTINGS]: settings
  });

      // 重新加载项目列表以更新显示状态
      await loadProjects();
    } catch (error) {
      console.error('Error saving project details setting:', error);
      showNotification('Failed to save setting', 'error');
    }
  });
};

// Initialize all functionality
const initialize = async () => {
  try {
    // 处理 URL 参数
    const urlParams = new URLSearchParams(window.location.search);
    const tab = urlParams.get('tab');
    const action = urlParams.get('action');

    // 初始化 DOM 元素
    initializeElements();

    // 初始化基础功能
    await initializeTabs();
    await initializeCollapse();

    // 初始化 API 相关功能
    setupVisibilityToggles();
    setupApiKeyValidation();
    await loadApiKeys();

    // 初始化 Token Usage 功能
    await loadTokenStats();
    initializeResetStats();
    initializeTokenUsageCollapse();

    // 初始化项目管理功能
    await initializeProjects();
    initializeProjectDetails();

    // 初始化引导功能
    initializeOnboardingGuide();

    // 添加设置变更监听
    Object.values(elements.settings).forEach(element => {
      if (element) {
        element.addEventListener('change', saveSettings);
      }
    });

    // 如果指定了标签页，切换到对应标签
    if (tab) {
      const tabButton = document.querySelector(`.nav-item[data-tab="${tab}"]`);
      if (tabButton) {
        tabButton.click();
      }
    }

    // 如果是从 popup 页面跳转来添加项目，等待一小段时间后打开 modal
    if (action === 'add_project') {
      setTimeout(() => {
        // 尝试通过 Vue 的方式打开模态弹窗
        window.openProjectModalVue && window.openProjectModalVue();
      }, 100);
    }
  } catch (error) {
    console.error('Error during initialization:', error);
    showNotification('Failed to initialize settings page', 'error');
  }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initialize);

// Delete project
async function deleteProject(projectId) {
  try {
    const storage = await chrome.storage.sync.get(STORAGE_KEYS.PROJECTS);
    const projects = storage[STORAGE_KEYS.PROJECTS] || [];

    const updatedProjects = projects.filter(p => p.id !== projectId);
    await chrome.storage.sync.set({ [STORAGE_KEYS.PROJECTS]: updatedProjects });
    await loadProjects();
    showNotification('Project deleted successfully', 'success');
  } catch (error) {
    console.error('Error deleting project:', error);
    showNotification('Failed to delete project', 'error');
  }
}

// Setup API key validation
function setupApiKeyValidation() {
  const providers = ['openai', 'claude', 'moonshot', 'gemini', 'deepseek'];

  providers.forEach(provider => {
    const input = document.getElementById(`${provider}-key`);
    if (!input) return;

    let validationTimeout;
    input.addEventListener('input', async () => {
      clearTimeout(validationTimeout);
      input.classList.remove('valid', 'invalid');

      const value = input.value.trim();

      // 如果输入为空，立即清除存储的 key
      if (!value) {
        try {
          const storage = await chrome.storage.sync.get([
            STORAGE_KEYS.VALIDATED_KEYS,
            STORAGE_KEYS.API_KEYS,
            STORAGE_KEYS.SETTINGS
          ]);

          const validatedKeys = storage[STORAGE_KEYS.VALIDATED_KEYS] || {};
          const apiKeys = storage[STORAGE_KEYS.API_KEYS] || {};
          const settings = storage[STORAGE_KEYS.SETTINGS] || {};

          // 删除对应的 key
          delete validatedKeys[provider];
          delete apiKeys[provider];

          // 如果清除的是当前默认提供商，且没有其他有效的 key，重置为 openai
          if (settings.defaultProvider === provider && Object.keys(validatedKeys).length === 0) {
            settings.defaultProvider = 'openai';
            if (elements.settings['default-provider']) {
              elements.settings['default-provider'].value = 'openai';
              // 更新模型选项为 OpenAI 的默认选项
              updateModelOptions('openai');
            }
          }

          await chrome.storage.sync.set({
            [STORAGE_KEYS.VALIDATED_KEYS]: validatedKeys,
            [STORAGE_KEYS.API_KEYS]: apiKeys,
            [STORAGE_KEYS.SETTINGS]: settings
          });

          return;
        } catch (error) {
          console.error('Error clearing API key:', error);
        }
      }

      // 对非空值进行验证
      validationTimeout = setTimeout(() => validateApiKey(provider, value), 500);
    });
  });
}

// 显示登录确认模态框
function showLoginConfirmModal() {
  const modal = document.getElementById('login-confirm-modal');
  modal.classList.add('show');
}

// 隐藏登录确认模态框
function hideLoginConfirmModal() {
  const modal = document.getElementById('login-confirm-modal');
  modal.classList.remove('show');
}

// 初始化登录确认模态框
// function initializeLoginConfirmModal() {
//   const modal = document.getElementById('login-confirm-modal');
//   const cancelBtn = document.getElementById('cancel-login');
//   const confirmBtn = document.getElementById('confirm-login');
//   const useCustomApiCheckbox = document.getElementById('use-custom-api');

//   // 点击取消按钮
//   cancelBtn.addEventListener('click', () => {
//     hideLoginConfirmModal();
//     useCustomApiCheckbox.checked = true; // 保持选中状态
//   });

//   // 点击确认按钮（跳转到登录页面）
//   confirmBtn.addEventListener('click', () => {
//     hideLoginConfirmModal();
//     chrome.tabs.create({ url: 'https://fillify.tech/signin' });
//     useCustomApiCheckbox.checked = true; // 保持选中状态
//   });

//   // 点击模态框背景关闭
//   modal.addEventListener('click', (e) => {
//     if (e.target === modal) {
//       hideLoginConfirmModal();
//       useCustomApiCheckbox.checked = true; // 保持选中状态
//     }
//   });
// }

// 添加测试数据（仅用于调试）
async function addTestTokenStats() {
  try {
    // 获取当前统计数据
    const storage = await chrome.storage.sync.get(STORAGE_KEYS.TOKEN_STATS);
    const stats = storage[STORAGE_KEYS.TOKEN_STATS] || {};

    // 添加测试数据
    stats.openai = {
      promptTokens: 1234,
      completionTokens: 5678,
      totalTokens: 6912,
      lastUpdated: new Date().toISOString()
    };

    // 保存测试数据
    await chrome.storage.sync.set({ [STORAGE_KEYS.TOKEN_STATS]: stats });
    console.log('Test token stats added:', stats);

    // 重新加载统计数据
    await loadTokenStats();

    // 显示成功通知
    showNotification('Test token stats added successfully', 'success');
  } catch (error) {
    console.error('Error adding test token stats:', error);
    showNotification('Failed to add test token stats', 'error');
  }
}

// 初始化测试按钮
function initializeTestTokenStats() {
  // 创建测试按钮
  const tokenStatsSection = document.querySelector('.token-stats-section');
  if (!tokenStatsSection) return;

  const testButton = document.createElement('button');
  testButton.textContent = 'Add Test Data';
  testButton.className = 'btn test-btn';
  testButton.style.marginLeft = '10px';

  // 添加点击事件
  testButton.addEventListener('click', addTestTokenStats);

  // 将按钮添加到重置按钮旁边
  const resetBtn = document.getElementById('reset-stats');
  if (resetBtn && resetBtn.parentNode) {
    resetBtn.parentNode.appendChild(testButton);
  }
}

// 在初始化函数中调用
function initialize() {
  // 初始化API提供商
  initializeApiProviders();

  // 初始化项目管理
  initializeProjects();

  // 初始化折叠功能
  initializeCollapse();

  // 初始化标签页导航
  initializeTabNavigation();

  // 初始化重置统计功能
  initializeResetStats();

  // 初始化Token统计折叠功能
  initializeTokenUsageCollapse();

  // 初始化项目详情切换
  initializeProjectDetails();

  // 初始化引导指南
  initializeOnboardingGuide();

  // 初始化API密钥验证
  setupApiKeyValidation();

  // 初始化测试Token统计功能
  initializeTestTokenStats();

  // 加载Token统计
  loadTokenStats();
}
